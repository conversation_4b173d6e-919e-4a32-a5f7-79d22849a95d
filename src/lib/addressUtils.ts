import { PUBLIC_GETADDRESS_API_KEY } from '$env/static/public';

export interface AddressResult {
  formatted_address: string[];
  postcode?: string;
  town?: string;
  district?: string;
  county?: string;
}

export interface GetAddressResponse {
  addresses: string[];
}

export async function lookupPostcode(postcode: string): Promise<AddressResult[]> {
  const apiKey = PUBLIC_GETADDRESS_API_KEY;
  
  if (!apiKey) {
    throw new Error('GetAddress API key not configured');
  }

  // Clean the postcode (remove spaces and convert to uppercase)
  const cleanPostcode = postcode.replace(/\s+/g, '').toUpperCase();
  
  if (!cleanPostcode) {
    throw new Error('Please enter a valid postcode');
  }

  try {
    const response = await fetch(`https://api.getAddress.io/find/${cleanPostcode}?api-key=${apiKey}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('Postcode not found');
      } else if (response.status === 401) {
        throw new Error('Invalid API key');
      } else if (response.status === 429) {
        throw new Error('Too many requests. Please try again later.');
      }
      throw new Error('Address lookup failed');
    }

    const data: GetAddressResponse = await response.json();
    
    console.log('GetAddress API response:', data);
    
    if (!data.addresses || data.addresses.length === 0) {
      throw new Error('No addresses found for this postcode');
    }

    // Convert the string array to AddressResult objects
    return data.addresses.map(addressString => {
      const parts = typeof addressString === 'string' ? addressString.split(', ') : [];
      return {
        formatted_address: parts,
        postcode: cleanPostcode
      };
    });
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to lookup address');
  }
}

export function formatAddress(address: AddressResult): string {
  return address.formatted_address.join(', ');
}

export function parseAddress(address: AddressResult): {
  addressLine1: string;
  addressLine2?: string;
  addressLine3?: string;
} {
  const parts = address.formatted_address;
  
  // GetAddress typically returns: [house number + street, area/district, town, county]
  // We want to map this to UK standard address lines
  
  let addressLine1 = '';
  let addressLine2 = '';
  let addressLine3 = '';
  
  if (parts.length >= 1) {
    addressLine1 = parts[0]; // House number and street
  }
  
  if (parts.length >= 2) {
    addressLine2 = parts[1]; // Area/district
  }
  
  if (parts.length >= 3) {
    addressLine3 = parts[2]; // Town/city
  }
  
  // If we have 4+ parts, combine the last parts for the town/city
  if (parts.length >= 4) {
    addressLine3 = parts.slice(2, -1).join(', '); // Everything except the last part (which is usually county)
  }
  
  return {
    addressLine1: addressLine1 || '',
    addressLine2: addressLine2 || undefined,
    addressLine3: addressLine3 || undefined
  };
}