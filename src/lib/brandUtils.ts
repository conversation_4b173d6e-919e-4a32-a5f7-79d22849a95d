import { productsData, type ProductData } from './products-data.js';

export interface BrandInfo {
  name: string;
  description: string;
  image: string;
  categories: string[];
  productCount: number;
}

export const brandDescriptions: Record<string, BrandInfo> = {
  'McAGRP': {
    name: 'McAG<PERSON>',
    description: 'McAGRP is a leading manufacturer of high-quality GRP (Glass Reinforced Plastic) roofing systems. Specializing in both overlay and standard systems, McAGRP provides innovative solutions for flat roofing applications with superior durability and weather resistance.',
    image: '/brand-images/mcagrp.svg',
    categories: [],
    productCount: 0
  },
  'KoverTek': {
    name: 'KoverTek',
    description: 'KoverTek delivers advanced roofing solutions with a focus on innovation and quality. Their comprehensive range of products includes modern roofing systems designed for commercial and residential applications.',
    image: '/brand-images/kovertek.png',
    categories: [],
    productCount: 0
  },
  'Restec': {
    name: 'Restec',
    description: 'Restec is renowned for their premium roofing restoration and maintenance products. With decades of experience, they offer reliable solutions for extending the life of existing roofing systems.',
    image: '/brand-images/restec-logo.png',
    categories: [],
    productCount: 0
  },
  'Eagle': {
    name: 'Eagle',
    description: 'Eagle provides professional-grade roofing materials known for their strength and reliability. Their products are trusted by contractors and building professionals across the industry.',
    image: '/brand-images/eagle.png',
    categories: [],
    productCount: 0
  },
  'IKO': {
    name: 'IKO',
    description: 'IKO is a global leader in roofing materials, offering innovative solutions for residential and commercial applications. Known for quality and performance, IKO products meet the highest industry standards.',
    image: '/brand-images/iko-logo.png',
    categories: [],
    productCount: 0
  },
  'Permaroof': {
    name: 'Permaroof',
    description: 'Permaroof specializes in liquid roofing systems and waterproofing solutions. Their products offer seamless protection and are ideal for complex roof geometries and challenging applications.',
    image: '/brand-images/permaroof-logo.png',
    categories: [],
    productCount: 0
  }
};

export function getAllBrands(): string[] {
  const brands = new Set<string>();
  productsData.forEach(product => {
    brands.add(product.brand);
  });
  return Array.from(brands).sort();
}

export function getBrandInfo(brandName: string): BrandInfo | null {
  const baseInfo = brandDescriptions[brandName];
  if (!baseInfo) return null;

  const brandProducts = getProductsByBrand(brandName);
  const categories = new Set<string>();
  
  brandProducts.forEach(product => {
    product.categories.forEach(category => {
      categories.add(category.trim());
    });
  });

  return {
    ...baseInfo,
    categories: Array.from(categories).sort(),
    productCount: brandProducts.length
  };
}

export function getProductsByBrand(brandName: string): ProductData[] {
  return productsData.filter(product => product.brand === brandName);
}

export function getAllBrandsWithInfo(): BrandInfo[] {
  return getAllBrands().map(brand => getBrandInfo(brand)).filter(Boolean) as BrandInfo[];
} 