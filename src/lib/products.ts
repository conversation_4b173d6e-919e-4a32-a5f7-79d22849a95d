import { productsData, type ProductData } from './products-data';
import { productsDetails, type ProductDetails } from './products-details';

export interface Product {
  productCode: string;
  slug: string;
  brand: string;
  name: string;
  price: number;
  htmlDescription: string;
  images: string[];
  metaDescription: string;
  categories: string[];
  size?: string;
}

// Function to combine product data with product details
function combineProductData(): Product[] {
  return productsData.map(data => {
    const details = productsDetails.find(detail => detail.productCode === data.productCode);
    
    if (!details) {
      console.warn(`No details found for product: ${data.productCode}`);
      return null;
    }

    return {
      productCode: data.productCode,
      brand: data.brand,
      name: data.name,
      price: data.price,
      categories: data.categories,
      size: data.size,
      slug: details.slug,
      htmlDescription: details.htmlDescription,
      images: details.images,
      metaDescription: details.metaDescription
    };
  }).filter(Boolean) as Product[];
}

// Generate the combined products array
export const products: Product[] = combineProductData();

export function getProductBySlug(slug: string): Product | undefined {
  return products.find(product => product.slug === slug);
}

export function getProductByCode(productCode: string): Product | undefined {
  return products.find(product => product.productCode === productCode);
}

export function getProducts(): Product[] {
  return products;
}

export function getAllCategories(): string[] {
  const categories = new Set<string>();
  products.forEach(product => {
    product.categories.forEach(category => categories.add(category));
  });
  return Array.from(categories).sort();
}

export function getProductsByCategory(category: string): Product[] {
  return products.filter(product => 
    product.categories.includes(category)
  );
}

export function getProductsByCategories(categories: string[]): Product[] {
  return products.filter(product => 
    categories.some(category => product.categories.includes(category))
  );
}

// Additional helper functions for the split data structure
export function getProductData(): ProductData[] {
  return productsData;
}

export function getProductDetails(): ProductDetails[] {
  return productsDetails;
}

// Function to refresh products (useful when automated data is updated)
export function refreshProducts(): Product[] {
  return combineProductData();
} 