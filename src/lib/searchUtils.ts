import { productsData, type ProductData } from './products-data.js';

export interface SearchResult {
  product: ProductData;
  matchType: 'name' | 'code' | 'both';
  score: number;
}

export function searchProducts(query: string, limit: number = 10): SearchResult[] {
  if (!query || query.length < 2) {
    return [];
  }

  const normalizedQuery = query.toLowerCase().trim();
  const results: SearchResult[] = [];

  productsData.forEach(product => {
    const nameMatch = product.name.toLowerCase().includes(normalizedQuery);
    const codeMatch = product.productCode.toLowerCase().includes(normalizedQuery);
    
    if (nameMatch || codeMatch) {
      let matchType: 'name' | 'code' | 'both';
      let score = 0;

      if (nameMatch && codeMatch) {
        matchType = 'both';
        score = 100;
      } else if (codeMatch) {
        matchType = 'code';
        score = 90; // Product code matches are highly relevant
      } else {
        matchType = 'name';
        score = 70;
      }

      // Boost score for exact matches
      if (product.name.toLowerCase() === normalizedQuery || product.productCode.toLowerCase() === normalizedQuery) {
        score += 30;
      }

      // Boost score for matches at the beginning
      if (product.name.toLowerCase().startsWith(normalizedQuery) || product.productCode.toLowerCase().startsWith(normalizedQuery)) {
        score += 20;
      }

      results.push({
        product,
        matchType,
        score
      });
    }
  });

  // Sort by score (highest first) and return limited results
  return results
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);
}

export function highlightMatch(text: string, query: string): string {
  if (!query) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
} 