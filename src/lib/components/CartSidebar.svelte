<script lang="ts">
  import { cart, cartActions } from '$lib/stores/cart.js';
  import type { CustomerInfo } from '$lib/stores/cart.js';
  import { goto } from '$app/navigation';
  import { lookupPostcode, parseAddress, type AddressResult } from '$lib/addressUtils.js';
  
  $: currentCart = $cart;
  $: subtotalPrice = cartActions.getTotalPrice(currentCart);
  $: vatAmount = subtotalPrice * 0.20;
  $: totalPrice = subtotalPrice + vatAmount;
  $: totalItems = cartActions.getTotalItems(currentCart);

  let isSubmitting = false;
  let submitMessage = '';
  let showCheckoutModal = false;
  let autoSaveIndicator = false;
  let addressLookupResults: AddressResult[] = [];
  let showAddressDropdown = false;
  let addressLookupError = '';
  let isLookingUpAddress = false;
  
  function updateQuantity(productCode: string, event: Event) {
    const target = event.target as HTMLInputElement;
    const quantity = parseInt(target.value);
    cartActions.updateQuantity(productCode, quantity);
  }
  
  function updateCustomerInfo(field: keyof CustomerInfo, event: Event) {
    const target = event.target as HTMLInputElement | HTMLTextAreaElement;
    cartActions.updateCustomerInfo({ [field]: target.value });
    
    // Show auto-save indicator
    autoSaveIndicator = true;
    setTimeout(() => {
      autoSaveIndicator = false;
    }, 2000);
  }
  
  function navigateToProduct(slug: string) {
    goto(`/products/${slug}`);
  }
  
  function openCheckoutModal() {
    showCheckoutModal = true;
  }
  
  function closeCheckoutModal() {
    showCheckoutModal = false;
  }
  
  function formatPrice(price: number): string {
    return price.toLocaleString('en-GB', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }
  
  async function handlePostcodeLookup() {
    const postcode = currentCart.customerInfo.postcode.trim();
    
    if (!postcode) {
      addressLookupError = 'Please enter a postcode';
      return;
    }
    
    isLookingUpAddress = true;
    addressLookupError = '';
    showAddressDropdown = false;
    
    try {
      addressLookupResults = await lookupPostcode(postcode);
      showAddressDropdown = true;
    } catch (error) {
      addressLookupError = error instanceof Error ? error.message : 'Address lookup failed';
      addressLookupResults = [];
      showAddressDropdown = false;
    } finally {
      isLookingUpAddress = false;
    }
  }
  
  function selectAddress(address: AddressResult) {
    console.log('Selected address:', address);
    console.log('Address parts:', address.formatted_address);
    
    const parsed = parseAddress(address);
    console.log('Parsed address:', parsed);
    
    cartActions.updateCustomerInfo({
      addressLine1: parsed.addressLine1,
      addressLine2: parsed.addressLine2,
      addressLine3: parsed.addressLine3
    });
    
    showAddressDropdown = false;
    addressLookupError = '';
    
    // Show auto-save indicator
    autoSaveIndicator = true;
    setTimeout(() => {
      autoSaveIndicator = false;
    }, 2000);
  }
  
  async function submitOrder() {
    if (currentCart.items.length === 0) {
      submitMessage = 'Please add items to your cart';
      return;
    }
    
    if (!currentCart.customerInfo.name || !currentCart.customerInfo.email || !currentCart.customerInfo.phone || 
        !currentCart.customerInfo.addressLine1 || !currentCart.customerInfo.postcode) {
      submitMessage = 'Please fill in all required fields';
      return;
    }
    
    isSubmitting = true;
    submitMessage = '';
    
    try {
      // This is where you would send to your webhook
      const orderData = {
        timestamp: new Date().toISOString(),
        customer: currentCart.customerInfo,
        items: currentCart.items.map(item => ({
          productCode: item.product.productCode,
          name: item.product.name,
          price: item.product.price,
          quantity: item.quantity,
          total: item.product.price * item.quantity
        })),
        totalPrice: totalPrice,
        totalItems: totalItems
      };
      
      console.log('Order data to be sent to webhook:', orderData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      submitMessage = 'Order submitted successfully! We will contact you soon.';
      cartActions.clearCart();
    } catch (error) {
      submitMessage = 'Error submitting order. Please try again.';
    } finally {
      isSubmitting = false;
    }
  }
</script>

<div class="cart-sidebar">
  <div class="order-form-header">
    <h2><i class="fas fa-clipboard-list"></i> Order Form</h2>
    <div class="items-count-badge">{totalItems} items</div>
  </div>
  
  <!-- Cart Items -->
  <div class="cart-section">
    {#if currentCart.items.length === 0}
      <p class="empty-cart">Your order is empty</p>
    {:else}
      <div class="cart-items">
        <div class="cart-items-header">
          <div class="header-item">Item</div>
          <div class="header-qty">Qty</div>
          <div class="header-total">Total</div>
          <div class="header-remove"></div>
        </div>
        {#each currentCart.items as item (item.product.productCode)}
          <div class="cart-item">
            <div class="cart-item-info">
              <img 
                src={item.product.images[0]} 
                alt={item.product.name}
                class="cart-item-image"
              />
              <div class="cart-item-details">
                <button 
                  class="cart-item-name clickable"
                  on:click={() => navigateToProduct(item.product.slug)}
                  title="View product details"
                >
                  {item.product.name}
                </button>
                <div class="cart-item-price">£{formatPrice(item.product.price)}</div>
              </div>
            </div>
            <div class="cart-item-quantity">
              <input 
                type="number" 
                min="1" 
                value={item.quantity}
                class="quantity-input"
                on:change={(e) => updateQuantity(item.product.productCode, e)}
              />
            </div>
            <div class="cart-item-total">£{(item.product.price * item.quantity).toFixed(2)}</div>
            <div class="cart-item-remove">
              <button 
                class="remove-btn"
                on:click={() => cartActions.removeItem(item.product.productCode)}
                title="Remove item"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        {/each}
      </div>
      
      <div class="cart-totals">
        <div class="total-line">
          <span class="total-label">Subtotal:</span>
          <span class="total-amount">£{formatPrice(subtotalPrice)}</span>
        </div>
        <div class="total-line">
          <span class="total-label">Delivery:</span>
          <span class="total-amount">TBA</span>
        </div>
        <div class="total-line">
          <span class="total-label">VAT (20%):</span>
          <span class="total-amount">£{formatPrice(vatAmount)}</span>
        </div>
        <div class="total-line total-final">
          <span class="total-label">Total:</span>
          <span class="total-amount">£{formatPrice(totalPrice)}</span>
        </div>
      </div>
    {/if}
  </div>
  
  <!-- Checkout Button -->
  <button 
    class="btn btn-primary checkout-btn"
    disabled={currentCart.items.length === 0}
    on:click={openCheckoutModal}
  >
    <i class="fas fa-credit-card"></i> Go to Checkout
  </button>
  
  {#if submitMessage}
    <div class="submit-message" class:success={submitMessage.includes('successfully')} class:error={submitMessage.includes('Error')}>
      {submitMessage}
    </div>
  {/if}
</div>

<!-- Checkout Modal -->
{#if showCheckoutModal}
  <div class="modal-overlay" on:click={closeCheckoutModal}>
    <div class="modal-content" on:click|stopPropagation>
      <div class="modal-header">
        <h2><i class="fas fa-credit-card"></i> Checkout</h2>
        <button class="modal-close" on:click={closeCheckoutModal}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
      
      <div class="modal-body">
        <!-- Left Column: Order Summary -->
        <div class="modal-column order-summary">
          <h3><i class="fas fa-list-alt"></i> Order Summary</h3>
          <div class="checkout-items">
            {#each currentCart.items as item (item.product.productCode)}
              <div class="checkout-item">
                <img 
                  src={item.product.images[0]} 
                  alt={item.product.name}
                  class="checkout-item-image"
                />
                <div class="checkout-item-details">
                  <div class="checkout-item-name">{item.product.name}</div>
                  <div class="checkout-item-meta">
                    <span class="checkout-item-price">£{formatPrice(item.product.price)}</span>
                    <span class="checkout-item-qty">Qty: {item.quantity}</span>
                  </div>
                  <div class="checkout-item-total">£{formatPrice(item.product.price * item.quantity)}</div>
                </div>
              </div>
            {/each}
          </div>
          <div class="checkout-totals">
            <div class="total-line">
              <span class="total-label">Subtotal:</span>
              <span class="total-amount">£{formatPrice(subtotalPrice)}</span>
            </div>
            <div class="total-line">
              <span class="total-label">Delivery:</span>
              <span class="total-amount">TBA</span>
            </div>
            <div class="total-line">
              <span class="total-label">VAT (20%):</span>
              <span class="total-amount">£{formatPrice(vatAmount)}</span>
            </div>
            <div class="total-line total-final">
              <span class="total-label">Total:</span>
              <span class="total-amount">£{formatPrice(totalPrice)}</span>
            </div>
          </div>
        </div>
        
        <!-- Middle Column: Customer Information -->
        <div class="modal-column customer-form">
          <div class="customer-form-header">
            <h3><i class="fas fa-user"></i> Customer Information</h3>
            {#if autoSaveIndicator}
              <div class="auto-save-indicator">
                <i class="fas fa-check-circle"></i> Auto-saved
              </div>
            {/if}
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-name">Name *</label>
            <input 
              type="text" 
              id="modal-name"
              class="form-input"
              value={currentCart.customerInfo.name}
              on:input={(e) => updateCustomerInfo('name', e)}
              required
            />
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-email">Email *</label>
            <input 
              type="email" 
              id="modal-email"
              class="form-input"
              value={currentCart.customerInfo.email}
              on:input={(e) => updateCustomerInfo('email', e)}
              required
            />
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-phone">Phone *</label>
            <input 
              type="tel" 
              id="modal-phone"
              class="form-input"
              value={currentCart.customerInfo.phone}
              on:input={(e) => updateCustomerInfo('phone', e)}
              required
            />
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-company">Company</label>
            <input 
              type="text" 
              id="modal-company"
              class="form-input"
              value={currentCart.customerInfo.company}
              on:input={(e) => updateCustomerInfo('company', e)}
            />
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-postcode">Postcode *</label>
            <div class="postcode-lookup">
              <input 
                type="text" 
                id="modal-postcode"
                class="form-input"
                value={currentCart.customerInfo.postcode}
                on:input={(e) => updateCustomerInfo('postcode', e)}
                placeholder="Enter postcode"
                required
              />
              <button 
                type="button"
                class="btn btn-secondary postcode-lookup-btn"
                on:click={handlePostcodeLookup}
                disabled={isLookingUpAddress || !currentCart.customerInfo.postcode.trim()}
              >
                {#if isLookingUpAddress}
                  <i class="fas fa-spinner fa-spin"></i>
                {:else}
                  <i class="fas fa-search"></i>
                {/if}
                Lookup
              </button>
            </div>
            {#if addressLookupError}
              <div class="address-error">{addressLookupError}</div>
            {/if}
            {#if showAddressDropdown}
              <div class="address-dropdown">
                <div class="address-dropdown-header">Select your address:</div>
                {#each addressLookupResults as address, index (index)}
                  <button 
                    type="button"
                    class="address-option"
                    on:click={() => selectAddress(address)}
                  >
                    {address.formatted_address ? address.formatted_address.join(', ') : 'Invalid address format'}
                  </button>
                {/each}
              </div>
            {/if}
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-address-line1">Address Line 1 *</label>
            <input 
              type="text" 
              id="modal-address-line1"
              class="form-input"
              value={currentCart.customerInfo.addressLine1}
              on:input={(e) => updateCustomerInfo('addressLine1', e)}
              placeholder="House number and street name"
              required
            />
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-address-line2">Address Line 2</label>
            <input 
              type="text" 
              id="modal-address-line2"
              class="form-input"
              value={currentCart.customerInfo.addressLine2 || ''}
              on:input={(e) => updateCustomerInfo('addressLine2', e)}
              placeholder="District or area"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-address-line3">Address Line 3</label>
            <input 
              type="text" 
              id="modal-address-line3"
              class="form-input"
              value={currentCart.customerInfo.addressLine3 || ''}
              on:input={(e) => updateCustomerInfo('addressLine3', e)}
              placeholder="Town or city"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label" for="modal-notes">Additional Notes</label>
            <textarea 
              id="modal-notes"
              class="form-textarea"
              value={currentCart.customerInfo.notes}
              on:input={(e) => updateCustomerInfo('notes', e)}
              placeholder="Any special requirements or notes..."
            ></textarea>
          </div>
          
        </div>
        
        <!-- Right Column: How It Works -->
        <div class="modal-column how-it-works">
          <h3><i class="fas fa-info-circle"></i> How It Works</h3>
          <div class="process-steps">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>Order Confirmation</h4>
                <p>Once you submit your order, you'll receive an email confirmation with your order details.</p>
              </div>
            </div>
            
            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>Order Review & Quote</h4>
                <p>Our team will review your order and contact you with an updated quote including delivery costs based on your location and order size.</p>
              </div>
            </div>
            
            <div class="step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>Quote Approval</h4>
                <p>We'll discuss the delivery options and final pricing with you. Once you approve the quote, we'll send you an invoice.</p>
              </div>
            </div>
            
            <div class="step">
              <div class="step-number">4</div>
              <div class="step-content">
                <h4>Payment & Delivery</h4>
                <p>After payment is received, we'll schedule delivery or arrange collection of your flat roofing materials at the agreed time.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Sticky Submit Button Footer -->
      <div class="modal-footer">
        {#if submitMessage}
          <div class="submit-message" class:success={submitMessage.includes('successfully')} class:error={submitMessage.includes('Error')}>
            {submitMessage}
          </div>
        {/if}
        <button 
          class="btn btn-primary submit-order-btn"
          disabled={isSubmitting || currentCart.items.length === 0}
          on:click={submitOrder}
        >
          {#if isSubmitting}
            <i class="fas fa-spinner fa-spin"></i> Submitting Order...
          {:else}
            <i class="fas fa-paper-plane"></i> Submit Order
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .cart-sidebar {
    max-height: calc(100vh - 80px - 4rem);
    overflow-y: auto;
    position: sticky !important;
    top: calc(80px + 2rem) !important;
    align-self: flex-start !important;
  }
  
  .order-form-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
  }
  
  .cart-sidebar h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    color: #1f2937;
  }
  
  .cart-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .cart-section:last-of-type {
    border-bottom: none;
  }
  
  
  .items-count-badge {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    border: 1px solid #e5e7eb;
  }
  
  .empty-cart {
    color: #6b7280;
    font-style: italic;
  }
  
  .cart-items {
    margin-bottom: 1rem;
  }
  
  .cart-items-header {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 0.75rem;
    padding: 0.5rem 0;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }
  
  .header-item {
    text-align: left;
  }
  
  .header-qty,
  .header-total {
    text-align: center;
    width: 70px;
  }
  
  .header-remove {
    width: 32px;
  }
  
  .cart-item {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
    align-items: center;
  }
  
  .cart-item:last-child {
    border-bottom: none;
  }
  
  .cart-item-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 0;
  }
  
  .cart-item-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
  }
  
  .cart-item-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .cart-item-name {
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.25;
    color: #1f2937;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    background: none;
    border: none;
    padding: 0;
    text-align: left;
    cursor: pointer;
    transition: color 0.2s;
  }
  
  .cart-item-name:hover {
    color: var(--primary-color);
    text-decoration: underline;
  }
  
  .cart-item-price {
    font-weight: 400;
    color: var(--secondary-color);
    font-size: 0.8rem;
  }
  
  .cart-item-total {
    font-weight: 600;
    color: var(--secondary-color);
    font-size: 0.875rem;
    text-align: center;
    width: 70px;
  }
  
  .cart-item-quantity {
    text-align: center;
    width: 70px;
  }
  
  .quantity-input {
    width: 50px;
    padding: 0.25rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    text-align: center;
    font-size: 0.875rem;
  }
  
  .cart-item-remove {
    text-align: center;
    width: 32px;
  }
  
  .remove-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
  }
  
  .remove-btn:hover {
    color: #dc2626;
    background-color: #fef2f2;
  }
  
  .cart-totals,
  .checkout-totals {
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
  }
  
  .total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    font-size: 0.95rem;
  }
  
  .total-line.total-final {
    border-top: 1px solid #e5e7eb;
    margin-top: 0.5rem;
    padding-top: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
  }
  
  .total-label {
    color: #374151;
  }
  
  .total-amount {
    color: var(--secondary-color);
    font-weight: 500;
  }
  
  .total-final .total-amount {
    font-weight: 600;
  }
  
  .submit-message {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 6px;
    font-weight: 600;
  }
  
  .submit-message.success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }
  
  .submit-message.error {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }
  
  .checkout-btn {
    width: 100%;
    margin-top: 1rem;
  }
  
  .checkout-btn:disabled {
    background-color: #9ca3af;
    color: #6b7280;
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  .checkout-btn:disabled:hover {
    background-color: #9ca3af;
  }
  
  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }
  
  .modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: column;
  }
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
  }
  
  .modal-header h2 {
    margin: 0;
    color: #1f2937;
    font-size: 1.5rem;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    color: #6b7280;
    border-radius: 6px;
    transition: all 0.2s;
  }
  
  .modal-close:hover {
    color: #374151;
    background-color: #e5e7eb;
  }
  
  .modal-body {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }
  
  .modal-column {
    display: flex;
    flex-direction: column;
  }
  
  .modal-column h3 {
    margin: 0 0 1.5rem 0;
    color: #1f2937;
    font-size: 1.25rem;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  /* Order Summary Column */
  .order-summary {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
  }
  
  .checkout-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .checkout-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
  }
  
  .checkout-item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    flex-shrink: 0;
  }
  
  .checkout-item-details {
    flex: 1;
  }
  
  .checkout-item-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
    font-size: 0.875rem;
    line-height: 1.25;
  }
  
  .checkout-item-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .checkout-item-price,
  .checkout-item-qty {
    font-size: 0.8rem;
    color: #6b7280;
  }
  
  .checkout-item-total {
    font-weight: 600;
    color: var(--secondary-color);
    font-size: 0.875rem;
  }
  
  
  /* Customer Form Column */
  .customer-form {
    overflow-y: auto;
    padding-right: 0.5rem;
    flex: 1;
    min-height: 0;
  }
  
  .customer-form-header {
    position: relative;
  }
  
  .auto-save-indicator {
    position: absolute;
    top: 0;
    right: 0;
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
    border-radius: 6px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: fadeInOut 2s ease-in-out;
  }
  
  @keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-10px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
  }
  
  /* Postcode Lookup Styles */
  .postcode-lookup {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
  }
  
  .postcode-lookup .form-input {
    flex: 1;
  }
  
  .postcode-lookup-btn {
    padding: 0.9rem 1rem;
    white-space: nowrap;
  }
  
  .postcode-lookup-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .address-error {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .address-dropdown {
    margin-top: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
  }
  
  .address-dropdown-header {
    padding: 0.75rem;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
  }
  
  .address-option {
    width: 100%;
    padding: 0.75rem;
    border: none;
    background: white;
    text-align: left;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s;
    font-size: 0.875rem;
    line-height: 1.4;
  }
  
  .address-option:hover {
    background: #f9fafb;
  }
  
  .address-option:last-child {
    border-bottom: none;
  }
  
  /* Modal Footer */
  .modal-footer {
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    padding: 1.5rem 2rem;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    flex-shrink: 0;
  }
  
  .submit-order-btn {
    width: 100%;
    font-size: 1.1rem;
    padding: 1rem 2rem;
  }
  
  /* How It Works Column */
  .how-it-works {
    background: #f0f9ff;
    padding: 1.5rem;
    border-radius: 8px;
  }
  
  .process-steps {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .step {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .step-number {
    width: 32px;
    height: 32px;
    background: var(--secondary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
  }
  
  .step-content h4 {
    margin: 0 0 0.5rem 0;
    color: #1f2937;
    font-size: 1rem;
    font-weight: 600;
  }
  
  .step-content p {
    margin: 0;
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.5;
  }
  
  /* Responsive Design */
  @media (max-width: 1024px) {
    .modal-body {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
    
    .modal-content {
      max-width: 800px;
    }
    
    .customer-form {
      overflow-y: visible;
    }
  }
  
  @media (max-width: 768px) {
    .modal-overlay {
      padding: 1rem;
    }
    
    .modal-header,
    .modal-body {
      padding: 1rem;
    }
    
    .modal-footer {
      padding: 1rem;
    }
    
    .order-summary,
    .how-it-works {
      padding: 1rem;
    }
    
    .submit-order-btn {
      font-size: 1rem;
      padding: 0.875rem 1.5rem;
    }
  }
</style> 