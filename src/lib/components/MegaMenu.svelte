<script lang="ts">
  import { categoryGroups, getCategoriesByGroup } from '$lib/categoryGroups.js';
  import { slugify } from '$lib/utils.js';
  import { afterNavigate, goto } from '$app/navigation';
  import { onDestroy } from 'svelte';
  
  let activeDropdown: string | null = null;
  let dropdownTimeout: number | null = null;
  
  function showDropdown(groupId: string) {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      dropdownTimeout = null;
    }
    activeDropdown = groupId;
  }
  
  function hideDropdown() {
    dropdownTimeout = window.setTimeout(() => {
      activeDropdown = null;
    }, 200);
  }
  
  function keepDropdownOpen() {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      dropdownTimeout = null;
    }
  }
  
  function resetDropdown() {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      dropdownTimeout = null;
    }
    activeDropdown = null;
  }
  
  function handleCategoryClick(event: Event, category: string) {
    event.preventDefault();
    resetDropdown();
    goto(`/categories/${slugify(category)}`);
  }
  
  function handleGroupClick(event: Event, groupId: string) {
    event.preventDefault();
    resetDropdown();
    goto(`/categories/group/${groupId}`);
  }
  
  // Reset dropdown state after navigation
  afterNavigate(() => {
    resetDropdown();
  });
  
  onDestroy(() => {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
    }
  });
</script>

<nav class="mega-menu">
  <div class="mega-menu-container">
    {#each categoryGroups as group}
      <div 
        class="menu-item"
        on:mouseenter={() => showDropdown(group.id)}
        on:mouseleave={hideDropdown}
      >
        <a href="/categories/group/{group.id}" class="menu-link" on:click={(e) => handleGroupClick(e, group.id)}>
          {group.name}
        </a>
        
        {#if activeDropdown === group.id}
          <div 
            class="dropdown-menu"
            on:mouseenter={keepDropdownOpen}
            on:mouseleave={hideDropdown}
          >
            <div class="dropdown-content">
              <div class="dropdown-header">
                <h3>{group.name}</h3>
                <p>{group.description}</p>
              </div>
              <div class="category-grid">
                {#each getCategoriesByGroup(group.id) as category}
                  <a href="/categories/{slugify(category)}" class="category-link" on:click={(e) => handleCategoryClick(e, category)}>
                    {category}
                  </a>
                {/each}
              </div>
            </div>
          </div>
        {/if}
      </div>
    {/each}
  </div>
</nav>

<style lang="less">
  .mega-menu {
    background: #28303d;
    border-bottom: 1px solid #4b5563;
    position: sticky;
    top: 83px;
    z-index: 900;
  }
  
  .mega-menu-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
  }
  
  .menu-item {
    position: relative;
  }
  
  .menu-link {
    display: block;
    color: white;
    text-decoration: none;
    padding: 1rem 1.5rem;
    font-weight: 500;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
  
  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    min-width: 400px;
    max-width: 600px;
    z-index: 1100;
    padding: 1.5rem;
  }
  
  .dropdown-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .dropdown-header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 1rem;
    
    h3 {
      margin: 0;
      color: #1f2937;
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    p {
      margin: 0.5rem 0 0 0;
      color: #6b7280;
      font-size: 0.9rem;
    }
  }
  
  .category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.5rem;
  }
  
  .category-link {
    display: block;
    color: #374151;
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.85rem;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    
    &:hover {
      background-color: #f3f4f6;
      border-color: #d1d5db;
      color: var(--primary-color);
      transform: translateY(-1px);
    }
  }
  
  /* Mobile Responsiveness */
  @media (max-width: 1024px) {
    .mega-menu {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .mega-menu-container {
      padding: 0 1rem;
      flex-wrap: wrap;
    }
    
    .menu-link {
      padding: 0.75rem 1rem;
      font-size: 0.8rem;
    }
    
    .dropdown-menu {
      min-width: 300px;
      max-width: 90vw;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .category-grid {
      grid-template-columns: 1fr;
    }
  }
</style>