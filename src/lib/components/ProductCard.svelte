<script lang="ts">
  import { cartActions } from '$lib/stores/cart.js';
  import { getCategorySlug } from '$lib/utils.js';
  import type { Product } from '$lib/products.js';
  import { getBrandInfo } from '$lib/brandUtils.js';
  
  export let product: Product;
  export let showCategories = false;
  export let showDescription = false;
  export let currentCategory: string | undefined = undefined;
  
  $: brandInfo = getBrandInfo(product.brand);
  
  function addToCart(event: Event) {
    event.stopPropagation(); // Prevent card click when button is clicked
    cartActions.addItem(product, 1);
  }
  
  function goToProduct() {
    window.location.href = `/products/${product.slug}`;
  }
</script>

<div class="product-card" on:click={goToProduct} role="button" tabindex="0" on:keydown={(e) => e.key === 'Enter' && goToProduct()}>
  <img src={product.images[0]} alt={product.name} class="product-image" />
  <div class="product-info">
    <div class="product-brand">
      {#if brandInfo?.image}
        <img src={brandInfo.image} alt={product.brand} class="brand-logo" />
      {:else}
        <span class="brand-text">{product.brand}</span>
      {/if}
    </div>
    <h3 class="product-name">{product.name}</h3>
    
    {#if showCategories}
      <div class="product-categories">
        {#each product.categories as category}
                     <a 
            href="/categories/{getCategorySlug(category)}" 
            class="category-tag"
            class:current={category === currentCategory}
            on:click={(e) => e.stopPropagation()}
          >
            {category}
          </a>
        {/each}
      </div>
    {/if}
    
    {#if showDescription}
      <div class="product-description">
        {@html product.htmlDescription.replace(/<[^>]*>/g, '').substring(0, 120)}...
      </div>
    {/if}
    
    <div class="product-price">£{product.price.toFixed(2)}</div>
    <div class="product-actions">
      <!-- <a href="/products/{product.slug}" class="btn btn-secondary">View Details</a> -->
              <button class="btn btn-primary" on:click={addToCart}>
          <i class="fas fa-plus"></i> Add to Order
        </button>
    </div>
  </div>
</div>

<style>
  .product-card {
    cursor: pointer;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }
  
  .product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  .product-categories {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
  }
  
  .category-tag {
    background: #e5e7eb;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s;
  }
  
  .category-tag:hover {
    background: #d1d5db;
  }
  
  .category-tag.current {
    background: var(--primary-color);
    color: white;
  }
  
  .product-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
  }
  
  .product-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  
  .product-actions .btn {
    flex: 1;
    min-width: 120px;
  }
  
  .brand-logo {
    height: 20px;
    max-width: 120px;
    object-fit: contain;
    object-position: left center;
  }
  
  .brand-text {
    color: #6b7280;
    font-size: 0.875rem;
  }
  
  .product-brand {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    min-height: 20px;
  }
  
  @media (max-width: 768px) {
    .product-actions {
      flex-direction: column;
    }
    
    .product-actions .btn {
      width: 100%;
    }
    
    .brand-logo {
      height: 18px;
      max-width: 100px;
    }
  }
</style> 