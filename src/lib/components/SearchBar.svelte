<script lang="ts">
  import { searchProducts, highlightMatch, type SearchResult } from '$lib/searchUtils.js';
  import { getProductByCode } from '$lib/products.js';
  import { createEventDispatcher, onMount } from 'svelte';
  import { goto } from '$app/navigation';
  
  const dispatch = createEventDispatcher();
  
  let searchInput: HTMLInputElement;
  let searchQuery = '';
  let searchResults: SearchResult[] = [];
  let isOpen = false;
  let selectedIndex = -1;
  let searchContainer: HTMLElement;
  let isSearchExpanded = false;
  let isMobile = false;
  
  // Debounce search
  let searchTimeout: number;
  
  $: {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      if (searchQuery.length >= 2) {
        searchResults = searchProducts(searchQuery, 8);
        isOpen = searchResults.length > 0;
        selectedIndex = -1;
      } else {
        searchResults = [];
        isOpen = false;
        selectedIndex = -1;
      }
    }, 300);
  }
  
  function handleKeydown(event: KeyboardEvent) {
    if (!isOpen) return;
    
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        selectedIndex = Math.min(selectedIndex + 1, searchResults.length - 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        selectedIndex = Math.max(selectedIndex - 1, -1);
        break;
      case 'Enter':
        event.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < searchResults.length) {
          selectResult(searchResults[selectedIndex]);
        } else if (searchQuery.trim()) {
          goto(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
          closeSearch();
        }
        break;
      case 'Escape':
        closeSearch();
        break;
    }
  }
  
  function selectResult(result: SearchResult) {
    // Get the actual product with proper slug
    const product = getProductByCode(result.product.productCode);
    if (product) {
      goto(`/products/${product.slug}`);
    } else {
      // Fallback to search results if product not found
      goto(`/products?search=${encodeURIComponent(result.product.productCode)}`);
    }
    closeSearch();
  }
  
  function closeSearch() {
    isOpen = false;
    selectedIndex = -1;
    searchQuery = '';
    if (isMobile) {
      isSearchExpanded = false;
    }
    searchInput?.blur();
  }
  
  function toggleSearch() {
    if (!isMobile) return; // Only toggle on mobile
    
    isSearchExpanded = !isSearchExpanded;
    if (isSearchExpanded) {
      setTimeout(() => {
        searchInput?.focus();
      }, 300);
    } else {
      closeSearch();
    }
  }
  
  function handleClickOutside(event: MouseEvent) {
    if (searchContainer && !searchContainer.contains(event.target as Node)) {
      isOpen = false;
      selectedIndex = -1;
      if (isMobile && !searchQuery) {
        isSearchExpanded = false;
      }
    }
  }
  
  function handleFocus() {
    if (searchQuery.length >= 2 && searchResults.length > 0) {
      isOpen = true;
    }
  }
  
  function checkMobile() {
    isMobile = window.innerWidth <= 1024;
    if (!isMobile) {
      isSearchExpanded = false; // Reset mobile state on desktop
    }
  }
  
  onMount(() => {
    checkMobile();
    window.addEventListener('resize', checkMobile);
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
      document.removeEventListener('click', handleClickOutside);
      clearTimeout(searchTimeout);
    };
  });
</script>

<div class="search-container" bind:this={searchContainer} class:mobile={isMobile} class:expanded={isSearchExpanded}>
  <div class="search-input-wrapper" class:expanded={isSearchExpanded || !isMobile}>
    {#if isMobile}
      <button class="search-toggle" on:click={toggleSearch} type="button">
        <i class="fas fa-search search-icon"></i>
      </button>
    {/if}
    
    <input
      bind:this={searchInput}
      bind:value={searchQuery}
      on:keydown={handleKeydown}
      on:focus={handleFocus}
      placeholder="Search products..."
      class="search-input"
      class:expanded={isSearchExpanded || !isMobile}
      type="text"
      autocomplete="off"
    />
    
    {#if !isMobile}
      <i class="fas fa-search search-icon-desktop"></i>
    {/if}
    
    {#if isMobile && isSearchExpanded}
      <button class="close-search" on:click={closeSearch} type="button">
        <i class="fas fa-times"></i>
      </button>
    {/if}
  </div>
  
  {#if isOpen && searchResults.length > 0}
    <div class="search-results" class:open={isOpen}>
      {#each searchResults as result, index}
        <button
          class="search-result-item"
          class:selected={index === selectedIndex}
          on:click={() => selectResult(result)}
        >
          <div class="result-main">
            <div class="result-name">
              {@html highlightMatch(result.product.name, searchQuery)}
            </div>
            <div class="result-code">
              {@html highlightMatch(result.product.productCode, searchQuery)}
            </div>
          </div>
          <div class="result-meta">
            <span class="result-price">£{result.product.price.toFixed(2)}</span>
            <span class="result-brand">{result.product.brand}</span>
          </div>
        </button>
      {/each}
      
      {#if searchQuery.trim()}
        <div class="search-all">
          <button 
            class="search-all-button"
            class:selected={selectedIndex === searchResults.length}
            on:click={() => {
              goto(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
              closeSearch();
            }}
          >
            View all results for "<strong>{searchQuery}</strong>"
          </button>
        </div>
      {/if}
    </div>
  {/if}
</div>

<style lang="less">
  .search-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    
    &.mobile {
      width: auto;
      max-width: none;
      
      &.expanded {
        position: fixed;
        top: 80px; /* Height of the sticky header */
        left: 0;
        right: 0;
        width: 100vw;
        max-width: none;
        z-index: 999;
        padding: 1rem;
        background: var(--primary-color);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    transition: all 0.3s ease;
  }

  .search-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }

  .search-icon {
    color: rgba(255, 255, 255, 0.9);
    font-size: 20px;
  }

  .search-icon-desktop {
    position: absolute;
    right: 1rem;
    color: rgba(255, 255, 255, 0.7);
    pointer-events: none;
    font-size: 16px;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 0px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }

    &:focus {
      outline: none;
      border-color: rgba(255, 255, 255, 0.6);
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
    }
  }

  .close-search {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.2s ease;
    
    &:hover {
      color: rgba(255, 255, 255, 1);
      background: rgba(255, 255, 255, 0.1);
    }
  }

  /* Mobile-specific styles */
  .search-container.mobile:not(.expanded) {
    .search-input {
      display: none;
    }
  }

  .search-container.mobile.expanded {
    .search-toggle {
      display: none;
    }
  }

  .search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 0.5rem;
    opacity: 0;
    transform: translateY(-10px);
    animation: slideIn 0.2s ease forwards;
  }

  .search-results.open {
    opacity: 1;
    transform: translateY(0);
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .search-result-item {
    width: 100%;
    padding: 1rem;
    border: none;
    background: white;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:hover,
    &.selected {
      background: #f8f9fa;
    }

    &:first-child {
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
    }

    &:last-of-type {
      border-bottom: none;
    }
  }

  .result-main {
    flex: 1;
    min-width: 0;
  }

  .result-name {
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .result-code {
    font-size: 0.85rem;
    color: #666;
    font-family: monospace;
  }

  .result-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    margin-left: 1rem;
  }

  .result-price {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
  }

  .result-brand {
    font-size: 0.8rem;
    color: #999;
    background: #f0f0f0;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
  }

  .search-all {
    border-top: 1px solid #e0e0e0;
  }

  .search-all-button {
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: white;
    text-align: center;
    cursor: pointer;
    color: var(--primary-color);
    font-weight: 500;
    transition: background-color 0.2s ease;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;

    &:hover,
    &.selected {
      background: #f8f9fa;
    }
  }

  :global(mark) {
    background: #fff3cd;
    color: #856404;
    padding: 0.1em 0.2em;
    border-radius: 3px;
  }

  /* Mobile adjustments */
  @media (max-width: 1024px) {
    .search-input {
      font-size: 16px; /* Prevents zoom on iOS */
    }
  }

  @media (max-width: 768px) {
    .search-results {
      max-height: 300px;
    }

    .result-meta {
      display: none; /* Hide price/brand on small screens */
    }
  }
</style> 