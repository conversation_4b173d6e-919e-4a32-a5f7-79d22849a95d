<script lang="ts">
  import SearchBar from './SearchBar.svelte';
  import { cart, cartActions } from '$lib/stores/cart.js';
  import { createEventDispatcher } from 'svelte';
  
  let mobileMenuOpen = false;
  
  const dispatch = createEventDispatcher();
  
  $: currentCart = $cart;
  $: totalItems = cartActions.getTotalItems(currentCart);
  
  function toggleMobileMenu() {
    mobileMenuOpen = !mobileMenuOpen;
  }
  
  function closeMobileMenu() {
    mobileMenuOpen = false;
  }
  
  function toggleMobileCart() {
    dispatch('toggle-mobile-cart');
  }
</script>

<header class="header">
  <div class="header-content">
    <h1><a href="/" on:click={closeMobileMenu}><img src="/McATEE-FLAT-ROOFING-SHOP.svg" alt="Flat Roofing Shop" class="frs-logo" /></a></h1>
    
    <div class="search-section">
      <SearchBar />
    </div>
    
    <nav class="desktop-nav">
      <a href="/"><i class="fas fa-home"></i> Home</a>
      <!-- <a href="/products">Products</a> -->
      <a href="/categories"><i class="fas fa-th-large"></i> Categories</a>
      <a href="/brands"><i class="fas fa-tags"></i> Brands</a>
      <!-- <a href="/about"><i class="fas fa-info-circle"></i> About</a> -->
      <a href="/contact"><i class="fas fa-envelope"></i> Contact</a>  
    </nav>
    
    <!-- Mobile Order Form Icon -->
    <button class="mobile-cart-toggle" on:click={toggleMobileCart} type="button">
      <i class="fas fa-clipboard-list"></i>
      {#if totalItems > 0}
        <span class="cart-badge">{totalItems}</span>
      {/if}
    </button>
    
    <button class="mobile-menu-toggle" on:click={toggleMobileMenu} type="button">
      <span></span>
      <span></span>
      <span></span>
    </button>
  </div>
  
  <!-- Mobile Menu Overlay -->
  {#if mobileMenuOpen}
    <div class="mobile-menu-overlay" on:click={closeMobileMenu}></div>
  {/if}
  
  <!-- Mobile Menu -->
  <nav class="mobile-menu" class:open={mobileMenuOpen}>
    <a href="/" on:click={closeMobileMenu}><i class="fas fa-home"></i> Home</a>
    <!-- <a href="/products" on:click={closeMobileMenu}>Products</a> -->
    <a href="/categories" on:click={closeMobileMenu}><i class="fas fa-th-large"></i> Categories</a>
    <a href="/brands" on:click={closeMobileMenu}><i class="fas fa-tags"></i> Brands</a>
    <a href="/about" on:click={closeMobileMenu}><i class="fas fa-info-circle"></i> About</a>
    <a href="/contact" on:click={closeMobileMenu}><i class="fas fa-envelope"></i> Contact</a>
  </nav>
</header>

<style lang="less">
  .header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    
    .frs-logo {
      width: 200px;
    }
  }

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: auto 1fr auto auto auto;
    gap: 2rem;
    align-items: center;
  }

  .header h1 a {
    color: white;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
  }

  .desktop-nav {
    display: flex;
    gap: 1rem;
  }

  .desktop-nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .desktop-nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .search-section {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .mobile-cart-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    color: white;
    position: relative;
    transition: all 0.2s;
    border-radius: 4px;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    i {
      font-size: 24px;
    }
  }
  
  .cart-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #dc2626;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    transform: translate(25%, -25%);
    border: 2px solid white;
    min-width: 20px;
  }

  .mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    gap: 0.25rem;
    
    span {
      width: 24px;
      height: 3px;
      background: white;
      transition: all 0.3s ease;
      border-radius: 2px;
    }
  }

  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1001;
  }

  .mobile-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100%;
    background: var(--primary-color);
    z-index: 1002;
    transition: right 0.3s ease;
    padding: 2rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    
    &.open {
      right: 0;
    }
    
    a {
      color: white;
      text-decoration: none;
      padding: 1rem;
      border-radius: 4px;
      transition: background-color 0.2s;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  @media (max-width: 1024px) {
    .header-content {
      grid-template-columns: auto 1fr auto auto;
      gap: 1rem;
    }
    
    .desktop-nav {
      display: none;
    }
    
    .mobile-menu-toggle {
      display: flex;
    }
    
    .mobile-cart-toggle {
      display: flex;
    }
  }

  @media (max-width: 768px) {
    .header-content {
      padding: 0 1rem;
    }
    
    .frs-logo {
      width: 150px !important;
    }
    
    .mobile-menu {
      width: 250px;
      right: -250px;
    }
  }
</style> 