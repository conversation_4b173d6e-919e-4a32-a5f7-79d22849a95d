export interface CategoryGroup {
  id: string;
  name: string;
  description: string;
  categories: string[];
}

export const categoryGroups: CategoryGroup[] = [
  {
    id: 'grp-roof-kits',
    name: 'GRP Roof Kits',
    description: 'Complete roofing systems and kits',
    categories: [
      'McAGRP Standard System',
      'McAGRP Overlay System', 
      'Restec Kits'
    ]
  },
  {
    id: 'grp-products',
    name: 'GRP Products',
    description: 'Core GRP materials and resins',
    categories: [
      'Basecoat',
      'Topcoat', 
      'Resin',
      'KoverTek TekGuard Resin',
      'KoverTek GRP Resin Resin',
      'Restec GRP 1010 Resins',
      'Restec GRP Fibreglass',
      'Restec GRP Catalyst',
      'Restec GRP Accelerators'
    ]
  },
  {
    id: 'repair-coatings',
    name: 'Repair Coatings',
    description: 'Repair and maintenance coatings',
    categories: [
      'Repair Coatings',
      'Restec Flexitec Products',
      'Restec Acryltex',
      'Restec Resgrip Anti-Slip Coating',
      'Restec Sealers'
    ]
  },
  {
    id: 'grp-tools',
    name: 'GRP Tools',
    description: 'Installation and application tools',
    categories: [
      'Restec Tools',
      'Permaroof Tools & Accessories'
    ]
  },
  {
    id: 'grp-trims',
    name: 'GRP Trims',
    description: 'Finishing trims and edging',
    categories: [
      'Trims',
      'Restec Trims',
      'Restec Corners',
      'Permaroof EPDM Trims & Fixings'
    ]
  },
  {
    id: 'grp-outlets',
    name: 'GRP Outlets',
    description: 'Drainage outlets and accessories',
    categories: [
      'Outlets',
      'Outlet',
      'Restec Outlets & Accessories',
      'Permaroof Drainage'
    ]
  },
  {
    id: 'reinforcements',
    name: 'Reinforcements',
    description: 'Reinforcement materials and fibers',
    categories: [
      'CSM',
      'Restec Flexitec CSM',
      'Restec Tapes & Tissues'
    ]
  },
  {
    id: 'epdm',
    name: 'EPDM',
    description: 'EPDM membrane systems and accessories',
    categories: [
      'EPDM',
      'Permaroof EPDM Membrane',
      'Permaroof EPDM Quickseam',
      'Permaroof PVC Membranes',
      'Permaroof Boards & Decking',
      'Permaroof Felt Membranes',
      'Vapor Control Layers',
      'Permaroof Adhesives',
      'Sealants & Cleaners'
    ]
  }
];

// Additional categories that need to be grouped
export const additionalCategoryMappings: Record<string, string> = {
  'Primer': 'grp-products',
  'KoverTek TekGuard Primer': 'grp-products', 
  'Restec Primers': 'grp-products',
  'Catalyst': 'grp-products',
  'KoverTek Catalyst': 'grp-products',
  'Acetone': 'grp-tools',
  'KoverTek Acetone': 'grp-tools',
  'Restec Solvents': 'grp-tools',
  'VCL/Carrier Layer & Bonding Agent': 'reinforcements',
  'Restec VCL & Carrier Layer': 'reinforcements',
  'VCL': 'reinforcements',
  'Restec Detailing': 'grp-products',
  'Restec Quartz Sands': 'grp-products',
  'Restec Chippings': 'grp-products',
  'Chippings': 'grp-products'
};

// Utility functions
export function getCategoryGroup(categoryName: string): CategoryGroup | null {
  // First check direct category mappings
  for (const group of categoryGroups) {
    if (group.categories.includes(categoryName)) {
      return group;
    }
  }
  
  // Then check additional mappings
  const groupId = additionalCategoryMappings[categoryName];
  if (groupId) {
    return categoryGroups.find(g => g.id === groupId) || null;
  }
  
  return null;
}

export function getCategoriesByGroup(groupId: string): string[] {
  const group = categoryGroups.find(g => g.id === groupId);
  if (!group) return [];
  
  // Get categories directly assigned to the group
  const directCategories = group.categories;
  
  // Get categories from additional mappings
  const additionalCategories = Object.entries(additionalCategoryMappings)
    .filter(([_, gId]) => gId === groupId)
    .map(([category, _]) => category);
  
  return [...directCategories, ...additionalCategories];
}

export function getAllCategoriesGrouped(): Record<string, string[]> {
  const result: Record<string, string[]> = {};
  
  for (const group of categoryGroups) {
    result[group.name] = getCategoriesByGroup(group.id);
  }
  
  return result;
}