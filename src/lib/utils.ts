/**
 * Converts a category name to a URL-safe slug
 * Handles special characters including spaces, slashes, ampersands, etc.
 */
export function getCategorySlug(categoryName: string): string {
  return categoryName
    .toLowerCase()
    .replace(/\s+/g, '-')        // Replace spaces with hyphens
    .replace(/[\/&]/g, '-')      // Replace slashes and ampersands with hyphens
    .replace(/[^a-z0-9\-]/g, '') // Remove any other special characters
    .replace(/-+/g, '-')         // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-|-$/g, '');      // Remove leading/trailing hyphens
}

/**
 * Alias for getCategorySlug for consistency
 */
export const slugify = getCategorySlug;

/**
 * Converts a URL slug back to a category name
 * This is the reverse of getCategorySlug
 */
export function slugToCategory(slug: string, allCategories: string[]): string | null {
  // Try to find exact match first (for simple cases)
  const directMatch = allCategories.find(cat => 
    getCategorySlug(cat) === slug
  );
  
  if (directMatch) {
    return directMatch;
  }
  
  // If no direct match, this might be an old URL format
  // Try the old format (just spaces to hyphens)
  const oldFormatMatch = allCategories.find(cat => 
    cat.toLowerCase().replace(/\s+/g, '-') === slug
  );
  
  return oldFormatMatch || null;
} 