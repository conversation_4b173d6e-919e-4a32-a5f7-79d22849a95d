export interface CategoryConfig {
  name: string;
  image: string;
  description?: string;
}

export const categoryImages: Record<string, CategoryConfig> = {
  'Membranes': {
    name: 'Membranes',
    image: '/category-images/membranes.jpg',
    description: 'Professional waterproof membrane systems for flat roofing'
  },
  'Insulation': {
    name: 'Insulation',
    image: '/category-images/insulation.jpg',
    description: 'High-performance insulation boards and materials'
  },
  'Adhesives': {
    name: 'Adhesives',
    image: '/category-images/adhesives.jpg',
    description: 'Professional bonding and sealing adhesives'
  },
  'Fasteners': {
    name: 'Fasteners',
    image: '/category-images/fasteners.jpg',
    description: 'Mechanical fixings and fastening systems'
  },
  'Drainage': {
    name: 'Drainage',
    image: '/category-images/drainage.jpg',
    description: 'Roof drainage systems and accessories'
  },
  'Accessories': {
    name: 'Accessories',
    image: '/category-images/accessories.jpg',
    description: 'Essential roofing tools and accessories'
  },
  'Ventilation': {
    name: 'Ventilation',
    image: '/category-images/ventilation.jpg',
    description: 'Roof ventilation systems and components'
  },
  'Sealants': {
    name: 'Sealants',
    image: '/category-images/sealants.jpg',
    description: 'Weather sealing and waterproofing compounds'
  }
};

export function getCategoryConfig(categoryName: string): CategoryConfig | null {
  return categoryImages[categoryName] || null;
}

export function getCategoryImage(categoryName: string): string {
  const config = getCategoryConfig(categoryName);
  return config?.image || '/category-images/placeholder.jpg';
}

export function getCategoryDescription(categoryName: string): string {
  const config = getCategoryConfig(categoryName);
  return config?.description || `Browse our ${categoryName.toLowerCase()} products for flat roofing applications.`;
}

export function getAllCategoryConfigs(): CategoryConfig[] {
  return Object.values(categoryImages);
}