import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import type { Product } from '../products.js';

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface CustomerInfo {
  name: string;
  email: string;
  phone: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  addressLine3?: string;
  postcode: string;
  notes?: string;
}

export interface Cart {
  items: CartItem[];
  customerInfo: CustomerInfo;
}

const defaultCustomerInfo: CustomerInfo = {
  name: '',
  email: '',
  phone: '',
  company: '',
  addressLine1: '',
  addressLine2: '',
  addressLine3: '',
  postcode: '',
  notes: ''
};

const initialCart: Cart = {
  items: [],
  customerInfo: defaultCustomerInfo
};

// Load cart from localStorage if available
function loadCartFromStorage(): Cart {
  if (browser && typeof localStorage !== 'undefined') {
    try {
      const storedCart = localStorage.getItem('flat-roofing-cart');
      if (storedCart) {
        const parsedCart = JSON.parse(storedCart);
        
        // Validate that the parsed cart has the correct structure
        if (parsedCart && typeof parsedCart === 'object') {
          return {
            items: Array.isArray(parsedCart.items) ? parsedCart.items : [],
            customerInfo: { 
              ...defaultCustomerInfo, 
              ...(parsedCart.customerInfo && typeof parsedCart.customerInfo === 'object' ? parsedCart.customerInfo : {})
            }
          };
        }
      }
    } catch (error) {
      console.error('Error loading cart from localStorage:', error);
      // Clear corrupted data
      try {
        localStorage.removeItem('flat-roofing-cart');
      } catch (clearError) {
        console.error('Error clearing corrupted cart data:', clearError);
      }
    }
  }
  return initialCart;
}

// Save cart to localStorage
function saveCartToStorage(cart: Cart) {
  if (browser && typeof localStorage !== 'undefined') {
    try {
      localStorage.setItem('flat-roofing-cart', JSON.stringify(cart));
    } catch (error) {
      console.error('Error saving cart to localStorage:', error);
    }
  }
}

export const cart = writable<Cart>(loadCartFromStorage());

// Subscribe to cart changes and save to localStorage
if (browser) {
  cart.subscribe((cartValue) => {
    saveCartToStorage(cartValue);
  });
}

export const cartActions = {
  addItem: (product: Product, quantity: number = 1) => {
    cart.update(currentCart => {
      const existingItemIndex = currentCart.items.findIndex(
        item => item.product.productCode === product.productCode
      );

      if (existingItemIndex >= 0) {
        currentCart.items[existingItemIndex].quantity += quantity;
      } else {
        currentCart.items.push({ product, quantity });
      }

      return currentCart;
    });
  },

  removeItem: (productCode: string) => {
    cart.update(currentCart => {
      currentCart.items = currentCart.items.filter(
        item => item.product.productCode !== productCode
      );
      return currentCart;
    });
  },

  updateQuantity: (productCode: string, quantity: number) => {
    cart.update(currentCart => {
      const itemIndex = currentCart.items.findIndex(
        item => item.product.productCode === productCode
      );

      if (itemIndex >= 0) {
        if (quantity <= 0) {
          currentCart.items.splice(itemIndex, 1);
        } else {
          currentCart.items[itemIndex].quantity = quantity;
        }
      }

      return currentCart;
    });
  },

  updateCustomerInfo: (info: Partial<CustomerInfo>) => {
    cart.update(currentCart => {
      currentCart.customerInfo = { ...currentCart.customerInfo, ...info };
      return currentCart;
    });
  },

  clearCart: () => {
    cart.set({
      items: [],
      customerInfo: defaultCustomerInfo
    });
  },

  getTotalPrice: (currentCart: Cart): number => {
    return currentCart.items.reduce(
      (total, item) => total + (item.product.price * item.quantity),
      0
    );
  },

  getTotalItems: (currentCart: Cart): number => {
    return currentCart.items.reduce((total, item) => total + item.quantity, 0);
  },

  // Utility function to clear localStorage if needed
  clearStoredCart: () => {
    if (browser && typeof localStorage !== 'undefined') {
      try {
        localStorage.removeItem('flat-roofing-cart');
      } catch (error) {
        console.error('Error clearing cart from localStorage:', error);
      }
    }
  }
}; 