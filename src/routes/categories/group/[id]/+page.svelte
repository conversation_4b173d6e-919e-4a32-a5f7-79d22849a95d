<script lang="ts">
  import { page } from '$app/stores';
  import { getProducts } from '$lib/products.js';
  import { categoryGroups, getCategoriesByGroup } from '$lib/categoryGroups.js';
  import { slugify } from '$lib/utils.js';
  
  $: groupId = $page.params.id;
  $: group = categoryGroups.find(g => g.id === groupId);
  $: groupCategories = group ? getCategoriesByGroup(group.id) : [];
  $: allProducts = getProducts();
  
  // Get products that belong to any category in this group
  $: groupProducts = allProducts.filter(product => 
    product.categories.some(category => groupCategories.includes(category))
  );
  
  // Group products by category for better organization
  $: productsByCategory = groupCategories.reduce((acc, category) => {
    const products = allProducts.filter(product => 
      product.categories.includes(category)
    );
    if (products.length > 0) {
      acc[category] = products;
    }
    return acc;
  }, {} as Record<string, typeof allProducts>);
</script>

<svelte:head>
  <title>{group?.name || 'Category Group'} - Flat Roofing Shop</title>
  <meta name="description" content="{group?.description || 'Browse our selection of products'} at Flat Roofing Shop." />
</svelte:head>

{#if group}
  <div class="group-page">
    <div class="group-header">
      <h1>{group.name}</h1>
      <p class="group-description">{group.description}</p>
      <div class="group-stats">
        <span>{groupProducts.length} products across {Object.keys(productsByCategory).length} categories</span>
      </div>
    </div>
    
    <div class="categories-section">
      <h2>Categories in {group.name}</h2>
      <div class="category-links">
        {#each groupCategories as category}
          {#if productsByCategory[category]}
            <a href="/categories/{slugify(category)}" class="category-card">
              <h3>{category}</h3>
              <span class="product-count">{productsByCategory[category].length} products</span>
            </a>
          {/if}
        {/each}
      </div>
    </div>
  </div>
{:else}
  <div class="error-page">
    <h1>Category Group Not Found</h1>
    <p>The requested category group could not be found.</p>
    <a href="/categories">← Back to Categories</a>
  </div>
{/if}

<style lang="less">
  .group-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .group-header {
    margin-bottom: 3rem;
    text-align: center;
    
    h1 {
      font-size: 2.5rem;
      color: #1f2937;
      margin-bottom: 1rem;
    }
    
    .group-description {
      font-size: 1.2rem;
      color: #6b7280;
      margin-bottom: 1rem;
    }
    
    .group-stats {
      color: #9ca3af;
      font-size: 0.9rem;
    }
  }
  
  .categories-section {
    h2 {
      font-size: 1.8rem;
      color: #374151;
      margin-bottom: 1.5rem;
    }
  }
  
  .category-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
  
  .category-card {
    display: block;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      border-color: var(--primary-color);
    }
    
    h3 {
      margin: 0 0 0.5rem 0;
      color: #1f2937;
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    .product-count {
      color: #6b7280;
      font-size: 0.9rem;
    }
  }
  
  .error-page {
    max-width: 600px;
    margin: 4rem auto;
    text-align: center;
    padding: 2rem;
    
    h1 {
      color: #dc2626;
      margin-bottom: 1rem;
    }
    
    p {
      margin-bottom: 2rem;
      color: #6b7280;
    }
    
    a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  @media (max-width: 768px) {
    .group-page {
      padding: 1rem;
    }
    
    .group-header h1 {
      font-size: 2rem;
    }
    
    .category-links {
      grid-template-columns: 1fr;
    }
  }
</style>