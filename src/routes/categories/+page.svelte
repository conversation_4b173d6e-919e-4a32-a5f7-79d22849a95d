<script lang="ts">
  import { getAllCategories, getProductsByCategory } from '$lib/products.js';
  import { getCategorySlug } from '$lib/utils.js';
  import { getCategoryImage, getCategoryDescription } from '$lib/categoryConfig.js';
  
  const allCategories = getAllCategories();
  
  function getCategoryProductCount(category: string): number {
    return getProductsByCategory(category).length;
  }
</script>

<svelte:head>
  <title>Product Categories - Flat Roofing Shop</title>
  <meta name="description" content="Browse our flat roofing products by category. Find membranes, insulation, adhesives, accessories, and drainage solutions organized by type." />
</svelte:head>

<div class="categories-page">
  <h1>Product Categories</h1>
  <p class="page-description">Browse our extensive range of flat roofing products organized by category. Find exactly what you need for your roofing project.</p>
  
  <div class="categories-grid">
    {#each allCategories as category}
      <div class="category-card">
        <div class="category-image">
          <img 
            src={getCategoryImage(category)} 
            alt={category}
            loading="lazy"
          />
        </div>
        <div class="category-content">
          <h2>
            <a href="/categories/{getCategorySlug(category)}">{category}</a>
          </h2>
          <p class="product-count">
            {getCategoryProductCount(category)} product{getCategoryProductCount(category) !== 1 ? 's' : ''}
          </p>
          <div class="category-description">
            <p>{getCategoryDescription(category)}</p>
          </div>
          <a href="/categories/{getCategorySlug(category)}" class="btn btn-primary">
            <i class="fas fa-arrow-right"></i> View Products
          </a>
        </div>
      </div>
    {/each}
  </div>
</div>

<style lang="less">
  .categories-page {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  h1 {
    color: #1f2937;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-align: center;
  }
  
  .page-description {
    text-align: center;
    color: #6b7280;
    font-size: 1.125rem;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .category-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .category-image {
    width: 100%;
    overflow: hidden;
    &>img {
      &[src="/category-images/placeholder.jpg"] {
        display: none;
        height: 200px;
      }
    }
  }
  
  .category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .category-card:hover .category-image img {
    transform: scale(1.05);
  }
  
  .category-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    flex: 1;
  }
  
  .category-content h2 {
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }
  
  .category-content h2 a {
    color: var(--primary-color);
    text-decoration: none;
  }
  
  .category-content h2 a:hover {
    text-decoration: underline;
  }
  
  .product-count {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  .category-description {
    color: #374151;
    line-height: 1.6;
    margin-bottom: 2rem;
    flex: 1;
  }
  
  .category-description p {
    margin: 0;
  }
  
  .btn {
    align-self: flex-start;
  }
  
  @media (max-width: 768px) {
    .categories-grid {
      grid-template-columns: 1fr;
    }
    
    h1 {
      font-size: 2rem;
    }
  }
</style> 