<script lang="ts">
  import ProductCard from '$lib/components/ProductCard.svelte';
  import type { Product } from '$lib/products.js';
  
  export let data: { category: string; products: Product[]; slug: string };
  
  $: category = data.category;
  $: products = data.products;
  $: slug = data.slug;
</script>

<svelte:head>
  <title>{category} - Flat Roofing Shop</title>
  <meta name="description" content="Browse our {category.toLowerCase()} products for flat roofing. High-quality materials from trusted brands with professional-grade performance." />
</svelte:head>

<div class="category-page">
  <div class="category-header">
    <nav class="breadcrumb">
      <a href="/">Home</a>
      <span>/</span>
      <a href="/categories">Categories</a>
      <span>/</span>
      <span>{category}</span>
    </nav>
    
    <h1>{category}</h1>
    <p class="category-description">
      {#if category === 'Membranes'}
        Professional waterproof membrane systems designed for long-lasting flat roof protection. Our range includes EPDM and TPO membranes suitable for both commercial and residential applications.
      {:else if category === 'Insulation'}
        High-performance insulation solutions that improve energy efficiency and meet building regulations. Essential for creating thermal barriers in flat roof systems.
      {:else if category === 'Adhesives'}
        Professional-grade bonding solutions specifically formulated for flat roofing applications. Weather-resistant formulas ensure secure, permanent bonds.
      {:else if category === 'Accessories'}
        Essential components and finishing accessories that complete your flat roofing installation. From trims to fixings, everything you need for a professional finish.
      {:else if category === 'Drainage'}
        Complete drainage solutions designed to prevent water accumulation and protect your flat roof structure from damage.
      {:else if category === 'EPDM'}
        Ethylene Propylene Diene Monomer rubber products offering superior weather resistance and longevity for flat roof applications.
      {:else if category === 'TPO'}
        Thermoplastic Polyolefin roofing systems that provide energy efficiency and excellent weather protection for commercial applications.
      {:else if category === 'Energy Efficient'}
        Products specifically designed to improve building energy performance, reduce heating costs, and meet environmental standards.
      {:else if category === 'Waterproofing'}
        Complete waterproofing solutions that provide reliable protection against water ingress in flat roof systems.
      {:else if category === 'Trim'}
        Professional finishing trims and edge details that provide clean, weather-resistant finishes to flat roof installations.
      {:else if category === 'Installation'}
        Essential tools and materials required for proper flat roof installation and maintenance.
      {:else}
        High-quality flat roofing products designed for professional installations and long-lasting performance.
      {/if}
    </p>
    
    <div class="product-count">
      {products.length} product{products.length !== 1 ? 's' : ''} found
    </div>
  </div>
  
  <div class="products-section">
    {#if products.length > 0}
      <div class="product-grid">
        {#each products as product, index (product.productCode)}
          <ProductCard 
            {product} 
            showCategories={true}
            showDescription={true}
            currentCategory={category}
          />
        {/each}
      </div>
    {:else}
      <div class="no-products">
        <h3>No products found in this category</h3>
        <p>This category doesn't have any products yet. Please check back later or browse other categories.</p>
        <a href="/categories" class="btn btn-primary">Browse All Categories</a>
      </div>
    {/if}
  </div>
</div>

<style>
  .category-page {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .category-header {
    margin-bottom: 2rem;
  }
  
  .breadcrumb {
    margin-bottom: 1rem;
    color: #6b7280;
    font-size: 0.875rem;
  }
  
  .breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
  }
  
  .breadcrumb a:hover {
    text-decoration: underline;
  }
  
  .breadcrumb span {
    margin: 0 0.5rem;
  }
  
  h1 {
    color: #1f2937;
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .category-description {
    color: #374151;
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    max-width: 800px;
  }
  
  .product-count {
    color: #6b7280;
    font-weight: 600;
    padding: 0.75rem 1rem;
    background: #f3f4f6;
    border-radius: 6px;
    display: inline-block;
  }
  
  .products-section {
    margin-top: 2rem;
  }
  

  
  .no-products {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .no-products h3 {
    color: #1f2937;
    margin-bottom: 1rem;
  }
  
  .no-products p {
    color: #6b7280;
    margin-bottom: 2rem;
  }
  
  @media (max-width: 768px) {
    h1 {
      font-size: 2rem;
    }
    
    .category-description {
      font-size: 1rem;
    }
  }
</style> 