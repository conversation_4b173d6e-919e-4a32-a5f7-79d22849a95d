import { getAllCategories, getProductsByCategory } from '$lib/products.js';
import { slugToCategory } from '$lib/utils.js';
import { error } from '@sveltejs/kit';

export const load = ({ params }) => {
  const allCategories = getAllCategories();
  const category = slugToCategory(params.slug, allCategories);
  
  if (!category) {
    throw error(404, 'Category not found');
  }
  
  const products = getProductsByCategory(category);
  
  return {
    category,
    products,
    slug: params.slug
  };
};

export const prerender = false; 