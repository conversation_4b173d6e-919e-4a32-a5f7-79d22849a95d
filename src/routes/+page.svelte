<script lang="ts">
  import { getProducts } from '$lib/products.js';
  import { onMount } from 'svelte';

</script>

<svelte:head>
  <title>Flat Roofing Shop - Professional Flat Roofing Materials & Supplies</title>
  <meta name="description" content="UK's leading supplier of flat roofing materials. EPDM rubber membranes, TPO, PIR insulation, adhesives, and professional roofing supplies. Quality products for commercial and residential projects." />
</svelte:head>

<!-- Hero Section with GRP Roofing Examples -->
<section class="hero-gallery">
  <div class="gallery-grid">
    <div class="gallery-item">
      <img src="/product-images/mcagrp-fibreglass-grp-roof-1.jpg" alt="GRP Roofing Installation" />
    </div>
    <div class="gallery-item">
      <img src="/product-images/Flat-Roofing-North-West-After-5.jpg" alt="GRP Skylight Installation" />
    </div>
    <div class="gallery-item">
      <img src="/product-images/WhatsApp-Image-2022-06-07-at-6.39.37-PM-1.jpeg" alt="GRP Roof Access Hatch" />
    </div>
    <div class="gallery-item">
      <img src="/product-images/WhatsApp-Image-2022-06-07-at-6.44.23-PM.jpeg" alt="GRP Dormer Installation" />
    </div>
  </div>
  <div class="hero-text">
    <h1>Experience the difference with our high-quality GRP systems</h1>
  </div>
</section>

<!-- McAGRP Systems Section -->
<section class="mcagrp-systems">
  <div class="system-grid">
    <div class="system-card overlay-system">
      <div class="system-content">
        <div class="product-showcase">
          <img src="/product-images/McAGRP-O-W.svg" alt="McAGRP Overlay System Products" />
        </div>
        <div class="system-picture">
          <img src="/product-images/2overlay-tins.png" alt="McAGRP Overlay System Products" />
        </div>
      </div>
    </div>
    
    <div class="system-card standard-system">
      <div class="system-content">
        <div class="product-showcase">
          <img src="/product-images/McAGRP-S-W.svg" alt="McAGRP Standard System Products" />
        </div>
        <div class="system-picture">
          <img src="/product-images/macgrp-standard.webp" alt="McAGRP Overlay System Products" />
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Shop by Category Section -->
<section class="shop-categories">
  <div class="category-header">
    <h2>Shop by Category</h2>
  </div>
  <div class="categories-grid">
    <div class="category-card">
      <div class="category-image">
        <img src="/product-images/macgrp-standard.webp" alt="Product Kits" />
      </div>
      <h3>Product Kits</h3>
    </div>
    
    <div class="category-card">
      <div class="category-image">
        <img src="/product-images/2overlay-tins.png" alt="GRP Products" />
      </div>
      <h3>GRP Products</h3>
    </div>
    
    <div class="category-card">
      <div class="category-image">
        <img src="/product-images/mcagrp-fibreglass-grp-roof-1.jpg" alt="Repair Coatings" />
      </div>
      <h3>Repair Coatings</h3>
    </div>
  </div>
</section>

<style>
  /* Hero Gallery Section */
  .hero-gallery {
    margin-bottom: 2rem;
  }
  
  .gallery-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .gallery-item {
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .hero-text {
    text-align: center;
    padding: 1rem 0;
  }
  
  .hero-text h1 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
  
  /* McAGRP Systems Section */
  .mcagrp-systems {
    margin-bottom: 2rem;
  }
  
  .system-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .system-card {
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    min-height: 200px;
  }
  
  .overlay-system {
    background: linear-gradient(135deg, #6B46C1 0%, #9333EA 100%);
    color: white;
  }
  
  .standard-system {
    background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%);
    color: white;
  }
  
  .system-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .system-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }
  
  .brand-logo {
    height: 24px;
    width: auto;
    filter: brightness(0) invert(1);
  }
  
  .system-title {
    display: flex;
    flex-direction: column;
  }

  .system-picture {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: slideUp 1s ease-out 0.5s both;
    
    img {
      height: 350px;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(40px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .product-name {
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
  }
  
  .system-type {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.05em;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 4px;
    align-self: flex-start;
    margin-top: 4px;
  }
  
  .product-showcase {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
  }
  
  .product-showcase img {
    max-width: 100%;
    max-height: 100px;
    object-fit: contain;
  }
  
  /* Shop by Category Section */
  .shop-categories {
    background: #F8F9FA;
    padding: 2rem;
    border-radius: 12px;
  }
  
  .category-header {
    background: #20B2AA;
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
  }
  
  .category-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  .categories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .category-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }
  
  .category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .category-image {
    aspect-ratio: 1;
    overflow: hidden;
  }
  
  .category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .category-card h3 {
    margin: 0;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
  }
  
  /* Mobile Responsiveness */
  @media (max-width: 768px) {
    .gallery-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .system-grid {
      grid-template-columns: 1fr;
    }
    
    .categories-grid {
      grid-template-columns: 1fr;
    }
    
    .hero-text h1 {
      font-size: 1.4rem;
    }
  }
  
  @media (max-width: 480px) {
    .gallery-grid {
      grid-template-columns: 1fr 1fr;
    }
    
    .shop-categories {
      padding: 1rem;
    }
    
    .category-header {
      padding: 1rem;
    }
  }
</style>
