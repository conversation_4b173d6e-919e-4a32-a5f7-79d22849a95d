<script lang="ts">
  import '../app.css';
  import Header from '$lib/components/Header.svelte';
  import MegaMenu from '$lib/components/MegaMenu.svelte';
  import CartSidebar from '$lib/components/CartSidebar.svelte';
  
  let mobileCartOpen = false;
  
  function toggleMobileCart() {
    mobileCartOpen = !mobileCartOpen;
  }
  
  function closeMobileCart() {
    mobileCartOpen = false;
  }
</script>

<div class="app">
  <Header on:toggle-mobile-cart={toggleMobileCart} />
  <MegaMenu />

  <main class="main-layout">
    <div class="content">
      <slot />
    </div>
    <div class="cart-container" class:mobile-open={mobileCartOpen}>
      {#if mobileCartOpen}
        <div class="mobile-cart-overlay" on:click={closeMobileCart}></div>
      {/if}
      <div class="cart-sidebar-wrapper">
        <CartSidebar />
        {#if mobileCartOpen}
          <button class="mobile-cart-close" on:click={closeMobileCart} type="button">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        {/if}
      </div>
    </div>
  </main>
</div>

<style lang="less">
  :root {
    --primary-color: #0E9098;
    --secondary-color: #28303d;
  }
  
  .cart-container {
    position: sticky;
    top: 165px;
    right: 0;
    width: 500px;
    height: 100%;
    z-index: 1;
    background: white;
  }
  
  .mobile-cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1001;
    display: none;
  }
  
  .cart-sidebar-wrapper {
    position: relative;
  }
  
  .mobile-cart-close {
    display: none;
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    color: #6b7280;
    z-index: 10;
    
    &:hover {
      color: #374151;
    }
    
    svg {
      width: 20px;
      height: 20px;
    }
  }
  
     @media (max-width: 768px) {
     .main-layout {
      //  padding-right: 0;
       flex-direction: column;
     }
     
     .cart-container {
       position: fixed;
       top: 0;
       left: 0;
       width: 100%;
       height: 100%;
       z-index: 1000;
       pointer-events: none;
       display: none;
       
       &.mobile-open {
         display: block;
         pointer-events: auto;
         
         .mobile-cart-overlay {
           display: block;
         }
         
         .cart-sidebar-wrapper {
           transform: translateX(0);
         }
         
         .mobile-cart-close {
           display: block;
         }
       }
     }
     
     .cart-sidebar-wrapper {
       position: fixed;
       top: 0;
       right: 0;
       width: 100%;
       height: 100%;
       z-index: 1002;
       transform: translateX(100%);
       transition: transform 0.3s ease;
       background: white;
       overflow-y: auto;
       padding: 4rem 1rem 1rem 1rem;
     }
   }
</style>