<script lang="ts">
  import { getAllBrandsWithInfo } from '$lib/brandUtils.js';
  
  const brands = getAllBrandsWithInfo();
</script>

<svelte:head>
  <title>Brands - Flat Roofing Shop</title>
  <meta name="description" content="Explore our range of premium roofing brands including McAGRP, KoverTek, Restec, Eagle, IKO, and Permaroof." />
</svelte:head>

<div class="brands-page">
  <div class="container">
    <h1>Our Roofing Brands</h1>
    <p class="intro">
      We partner with the industry's leading manufacturers to bring you the highest quality roofing materials and systems. 
      Each brand in our collection has been carefully selected for their proven track record of excellence and innovation.
    </p>
    
    <div class="brands-grid">
      {#each brands as brand}
        <div class="brand-card">
          <a href="/brands/{brand.name}" class="brand-link">
            <div class="brand-image">
              <img src={brand.image} alt="{brand.name} logo" on:error={(e) => (e.currentTarget as HTMLImageElement).src = '/brand-images/placeholder.svg'} />
            </div>
            <div class="brand-content">
              <h2>{brand.name}</h2>
              <p class="brand-description">{brand.description}</p>
              <div class="brand-stats">
                <span class="product-count">{brand.productCount} products</span>
                <span class="category-count">{brand.categories.length} categories</span>
              </div>
            </div>
          </a>
        </div>
      {/each}
    </div>
  </div>
</div>

<style lang="less">
  .brands-page {
    padding: 2rem 0;
    min-height: 60vh;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  h1 {
    text-align: center;
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .intro {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }

  .brands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .brand-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
    background: white;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
  }

  .brand-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
  }

  .brand-image {
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;

    img {
      max-width: 100%;
      max-height: 100%;
      max-height: 80px;
      object-fit: contain;
    }
  }

  .brand-content {
    padding: 1.5rem;
  }

  .brand-content h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
  }

  .brand-description {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .brand-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
  }

  .product-count,
  .category-count {
    background: #f0f0f0;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 1rem;
    }

    h1 {
      font-size: 2rem;
    }

    .brands-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .brand-content {
      padding: 1rem;
    }
  }
</style> 