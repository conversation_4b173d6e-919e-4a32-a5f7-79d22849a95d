<script lang="ts">
  import { page } from '$app/stores';
  import { getBrandInfo } from '$lib/brandUtils.js';
  import type { BrandInfo } from '$lib/brandUtils.js';
  import { getProducts } from '$lib/products.js';
  import type { Product } from '$lib/products.js';
  import ProductCard from '$lib/components/ProductCard.svelte';
  
  $: slug = $page.params.slug;
  $: brandName = slug.charAt(0).toUpperCase() + slug.slice(1);
  $: brand = getBrandInfo(brandName) as BrandInfo | null;
  
  // Get all products and filter by brand - this gives us proper Product objects with correct slugs
  $: allProducts = getProducts();
  $: products = brand ? allProducts.filter(product => product.brand === brand.name) : [];
  $: sampleProducts = products.slice(0, 6); // Show first 6 products as samples
</script>

<svelte:head>
  <title>{brand?.name || 'Brand'} - Flat Roofing Shop</title>
  <meta name="description" content="{brand?.description || 'Premium roofing products'}" />
</svelte:head>

{#if brand}
  <div class="brand-page">
    <div class="container">
      <!-- Brand Header -->
      <div class="brand-header">
        <div class="brand-logo">
                     <img src={brand.image} alt="{brand.name} logo" on:error={(e) => (e.currentTarget as HTMLImageElement).src = '/brand-images/placeholder.svg'} />
        </div>
        <div class="brand-info">
          <h1>{brand.name}</h1>
          <p class="brand-description">{brand.description}</p>
          <div class="brand-stats">
            <div class="stat">
              <span class="stat-number">{brand.productCount}</span>
              <span class="stat-label">Products</span>
            </div>
            <div class="stat">
              <span class="stat-number">{brand.categories.length}</span>
              <span class="stat-label">Categories</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Categories Section -->
      <section class="categories-section">
        <h2>Product Categories</h2>
        <div class="categories-grid">
          {#each brand.categories as category}
            <div class="category-tag">
              {category}
            </div>
          {/each}
        </div>
      </section>

      <!-- Sample Products Section -->
      <section class="products-section">
        <div class="section-header">
          <h2>Featured Products</h2>
          <a href="/products?brand={brand.name.toLowerCase()}" class="view-all-link">
            View All {brand.productCount} Products →
          </a>
        </div>
        
        <div class="products-grid">
          {#each sampleProducts as product}
            <ProductCard 
              {product} 
              showCategories={true}
              showDescription={false}
            />
          {/each}
        </div>
      </section>
    </div>
  </div>
{:else}
  <div class="error-page">
    <div class="container">
      <h1>Brand Not Found</h1>
      <p>The brand "{brandName}" could not be found.</p>
      <a href="/brands" class="back-link">← Back to Brands</a>
    </div>
  </div>
{/if}

<style lang="less">
  .brand-page, .error-page {
    padding: 2rem 0;
    min-height: 60vh;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .brand-header {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
    align-items: center;
  }

  .brand-logo {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      max-width: 100%;
      max-height: 200px;
      object-fit: contain;
    }
  }

  .brand-info h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .brand-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 2rem;
  }

  .brand-stats {
    display: flex;
    gap: 2rem;
  }

  .stat {
    text-align: center;
  }

  .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
  }

  .stat-label {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .categories-section, .products-section, .gallery-section {
    margin-bottom: 3rem;
  }

  .categories-section h2, .products-section h2, .gallery-section h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }

  .categories-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .category-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }



  .gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .gallery-item {
    aspect-ratio: 4/3;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .error-page {
    text-align: center;
  }

  .error-page h1 {
    color: var(--primary-color);
    margin-bottom: 1rem;
  }

  .back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 1rem;
    }

    .brand-header {
      grid-template-columns: 1fr;
      gap: 2rem;
      text-align: center;
    }

    .brand-logo {
      max-width: 250px;
      margin: 0 auto;
    }

    .brand-info h1 {
      font-size: 2rem;
    }

    .brand-stats {
      justify-content: center;
    }

    .section-header {
      flex-direction: column;
      align-items: start;
      gap: 1rem;
    }

    .products-grid {
      grid-template-columns: 1fr;
    }
  }
</style> 