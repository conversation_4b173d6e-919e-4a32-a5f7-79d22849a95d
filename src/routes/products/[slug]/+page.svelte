<script lang="ts">
  import { cartActions } from '$lib/stores/cart.js';
  import type { Product } from '$lib/products.js';
  import { getBrandInfo } from '$lib/brandUtils.js';
  
  export let data: { product: Product };
  $: product = data.product;
  $: brandInfo = getBrandInfo(product.brand);
  
  let quantity = 1;
  let currentImageIndex = 0;
  
  function addToCart() {
    cartActions.addItem(product, quantity);
  }
  
  function changeImage(index: number) {
    currentImageIndex = index;
  }
</script>

<svelte:head>
  <title>{product.name} - Flat Roofing Shop</title>
  <meta name="description" content={product.metaDescription} />
</svelte:head>

<div class="product-detail">
  <div class="product-images">
    <div class="main-image">
      <img src={product.images[currentImageIndex]} alt={product.name} />
    </div>
    {#if product.images.length > 1}
      <div class="image-thumbnails">
        {#each product.images as image, index}
          <button 
            class="thumbnail"
            class:active={index === currentImageIndex}
            on:click={() => changeImage(index)}
          >
            <img src={image} alt={product.name} />
          </button>
        {/each}
      </div>
    {/if}
  </div>
  
  <div class="product-details">
    <div class="product-brand">
      {#if brandInfo?.image}
        <img src={brandInfo.image} alt={product.brand} class="brand-logo" />
      {:else}
        <span class="brand-text">{product.brand}</span>
      {/if}
    </div>
    <h1 class="product-name">{product.name}</h1>
    <div class="product-code">Product Code: {product.productCode}</div>
    <div class="product-price">£{product.price.toFixed(2)}</div>
    
    <div class="product-description">
      {@html product.htmlDescription}
    </div>
    
    <div class="add-to-cart-section">
      <div class="quantity-selector">
        <label for="quantity">Quantity:</label>
        <input 
          type="number" 
          id="quantity"
          min="1" 
          bind:value={quantity}
          class="form-input"
        />
      </div>
      <button class="btn btn-primary btn-large" on:click={addToCart}>
        <i class="fas fa-plus"></i> Add to Order
      </button>
    </div>
  </div>
</div>

<style>
  .product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
  }
  
  .product-images {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .main-image {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .main-image img {
    width: 100%;
    height: auto;
    object-fit: contain;
  }
  
  .image-thumbnails {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
  }
  
  .thumbnail {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border: 2px solid transparent;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    background: none;
    padding: 0;
    transition: border-color 0.2s;
  }
  
  .thumbnail:hover,
  .thumbnail.active {
    border-color: var(--secondary-color);
  }
  
  .thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .product-details {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    height: fit-content;
  }
  
  .product-brand {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    min-height: 24px;
  }
  
  .brand-logo {
    height: 24px;
    max-width: 150px;
    object-fit: contain;
    object-position: left center;
  }
  
  .brand-text {
    color: #6b7280;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .product-name {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #1f2937;
  }
  
  .product-code {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }
  
  .product-price {
    font-size: 2rem;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 2rem;
  }
  
  .product-description {
    color: #374151;
    line-height: 1.7;
    margin-bottom: 2rem;
  }
  
  .product-description :global(ul) {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }
  
  .product-description :global(li) {
    margin-bottom: 0.5rem;
  }
  
  .product-description :global(p) {
    margin-bottom: 1rem;
  }
  
  .add-to-cart-section {
    display: flex;
    gap: 1rem;
    align-items: end;
  }
  
  .quantity-selector {
    flex-shrink: 0;
  }
  
  .quantity-selector label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
  }
  
  .quantity-selector input {
    width: 80px;
  }
  
  .btn-large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
  }
  
  @media (max-width: 768px) {
    .product-detail {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
    
    .product-name {
      font-size: 1.5rem;
    }
    
    .add-to-cart-section {
      flex-direction: column;
      align-items: stretch;
    }
    
    .quantity-selector input {
      width: 100%;
    }
    
    .brand-logo {
      height: 20px;
      max-width: 120px;
    }
  }
</style> 