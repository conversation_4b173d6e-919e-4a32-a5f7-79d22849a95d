<script lang="ts">
  import { getProducts, getProductByCode } from '$lib/products.js';
  import { searchProducts } from '$lib/searchUtils.js';
  import { page } from '$app/stores';
  import ProductCard from '$lib/components/ProductCard.svelte';
  import type { Product } from '$lib/products.js';
  
  const allProducts = getProducts();
  
  $: searchQuery = $page.url.searchParams.get('search') || '';
  $: brandFilter = $page.url.searchParams.get('brand') || '';
  
  $: filteredProducts = (() => {
    let products = allProducts;
    
    // If there's a search query, use the search function
    if (searchQuery.trim()) {
      const searchResults = searchProducts(searchQuery.trim(), 1000); // Get many results
      // Map search results to actual Product objects with proper slugs
      products = searchResults
        .map(result => getProductByCode(result.product.productCode))
        .filter(Boolean) as Product[];
    }
    
    // Apply brand filter if specified
    if (brandFilter.trim()) {
      products = products.filter(product => 
        product.brand.toLowerCase() === brandFilter.toLowerCase()
      );
    }
    
    return products;
  })();
  

  
  function clearFilters() {
    window.history.pushState({}, '', '/products');
  }
</script>

<svelte:head>
  <title>All Products - Flat Roofing Shop</title>
  <meta name="description" content="Browse our complete range of flat roofing products including EPDM membranes, TPO, insulation, adhesives, trims, and drainage solutions. Professional quality materials." />
</svelte:head>

{#if searchQuery || brandFilter}
  <div class="search-info">
    <h1>
      {#if searchQuery}
        Search Results for "{searchQuery}"
      {:else if brandFilter}
        Products from {brandFilter}
      {/if}
    </h1>
    <p class="results-count">{filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found</p>
    <button class="clear-filters-btn" on:click={clearFilters}>
      Clear Filters
    </button>
  </div>
{:else}
  <h1>All Products</h1>
{/if}

<div class="product-grid">
  {#each filteredProducts as product}
    <ProductCard 
      {product} 
      showCategories={true}
      showDescription={true}
    />
  {/each}
</div>

<style>
  h1 {
    margin-bottom: 2rem;
    color: #1f2937;
    font-size: 2.5rem;
  }
  

  
  .search-info {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
  }
  
  .search-info h1 {
    margin-bottom: 0.5rem;
    font-size: 2rem;
  }
  
  .results-count {
    color: #6b7280;
    margin-bottom: 1rem;
    font-size: 1rem;
  }
  
  .clear-filters-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
  }
  
  .clear-filters-btn:hover {
    background: #2563eb;
  }
</style> 