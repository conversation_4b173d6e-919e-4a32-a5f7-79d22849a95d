<svelte:head>
  <title>Contact Us - Flat Roofing Shop</title>
  <meta name="description" content="Get in touch with Flat Roofing Shop. Contact our expert team for product advice, quotes, and support. Phone, email, and address details." />
</svelte:head>

<div class="contact-content">
  <h1>Contact Us</h1>
  
  <div class="contact-grid">
    <div class="contact-info">
      <h2>Get in Touch</h2>
      <p>Our expert team is here to help with product selection, technical advice, and any questions about flat roofing materials. Contact us using any of the methods below.</p>
      
      <div class="contact-methods">
        <div class="contact-method">
          <h3>📞 Phone</h3>
          <p><strong>Sales:</strong> 01234 567890</p>
          <p><strong>Technical Support:</strong> 01234 567891</p>
          <p><em>Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 8:00 AM - 4:00 PM</em></p>
        </div>
        
        <div class="contact-method">
          <h3>✉️ Email</h3>
          <p><strong>General Enquiries:</strong><br><a href="mailto:<EMAIL>"><EMAIL></a></p>
          <p><strong>Sales:</strong><br><a href="mailto:<EMAIL>"><EMAIL></a></p>
          <p><strong>Technical Support:</strong><br><a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
        
        <div class="contact-method">
          <h3>🏢 Address</h3>
          <p>
            <strong>Flat Roofing Shop Ltd</strong><br>
            Unit 15, Industrial Estate<br>
            Roofing Way<br>
            Manchester M1 2AB<br>
            United Kingdom
          </p>
        </div>
        
        <div class="contact-method">
          <h3>🕒 Opening Hours</h3>
          <p>
            <strong>Warehouse & Trade Counter:</strong><br>
            Monday - Friday: 7:30 AM - 5:30 PM<br>
            Saturday: 8:00 AM - 4:00 PM<br>
            Sunday: Closed
          </p>
          <p>
            <strong>Office Hours:</strong><br>
            Monday - Friday: 8:00 AM - 6:00 PM<br>
            Saturday: 8:00 AM - 4:00 PM
          </p>
        </div>
      </div>
    </div>
    
    <div class="contact-form-section">
      <h2>Quick Enquiry</h2>
      <p>Use our order form on the right to place orders, or fill out this form for general enquiries and we'll get back to you within 24 hours.</p>
      
      <form class="contact-form">
        <div class="form-group">
          <label class="form-label" for="enquiry-name">Name *</label>
          <input type="text" id="enquiry-name" class="form-input" required />
        </div>
        
        <div class="form-group">
          <label class="form-label" for="enquiry-email">Email *</label>
          <input type="email" id="enquiry-email" class="form-input" required />
        </div>
        
        <div class="form-group">
          <label class="form-label" for="enquiry-phone">Phone</label>
          <input type="tel" id="enquiry-phone" class="form-input" />
        </div>
        
        <div class="form-group">
          <label class="form-label" for="enquiry-company">Company</label>
          <input type="text" id="enquiry-company" class="form-input" />
        </div>
        
        <div class="form-group">
          <label class="form-label" for="enquiry-subject">Subject *</label>
          <select id="enquiry-subject" class="form-input" required>
            <option value="">Please select...</option>
            <option value="product-enquiry">Product Enquiry</option>
            <option value="technical-support">Technical Support</option>
            <option value="quote-request">Quote Request</option>
            <option value="delivery-enquiry">Delivery Enquiry</option>
            <option value="general">General Enquiry</option>
          </select>
        </div>
        
        <div class="form-group">
          <label class="form-label" for="enquiry-message">Message *</label>
          <textarea id="enquiry-message" class="form-textarea" required placeholder="Please provide details of your enquiry..."></textarea>
        </div>
        
        <button type="submit" class="btn btn-primary">Send Enquiry</button>
      </form>
    </div>
  </div>
  
  <div class="additional-info">
    <h2>Additional Information</h2>
    
    <div class="info-grid">
      <div class="info-card">
        <h3>🚛 Delivery</h3>
        <p>We offer nationwide delivery across the UK. Standard delivery is typically 3-5 working days, with express options available for urgent orders.</p>
      </div>
      
      <div class="info-card">
        <h3>💳 Payment</h3>
        <p>We accept all major credit cards, bank transfers, and offer trade account facilities for regular customers.</p>
      </div>
      
      <div class="info-card">
        <h3>📋 Technical Data</h3>
        <p>Full technical specifications, installation guides, and product data sheets are available for all our products.</p>
      </div>
      
      <div class="info-card">
        <h3>🎓 Training</h3>
        <p>We offer product training sessions and installation workshops for contractors and specifiers.</p>
      </div>
    </div>
  </div>
</div>

<style>
  .contact-content {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  h1 {
    color: #1f2937;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
  }
  
  .contact-info,
  .contact-form-section {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .contact-info h2,
  .contact-form-section h2 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }
  
  .contact-info p,
  .contact-form-section p {
    color: #374151;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
  
  .contact-methods {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }
  
  .contact-method h3 {
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
  }
  
  .contact-method p {
    margin-bottom: 0.5rem;
    color: #374151;
  }
  
  .contact-method a {
    color: var(--secondary-color);
    text-decoration: none;
  }
  
  .contact-method a:hover {
    text-decoration: underline;
  }
  
  .contact-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .additional-info {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .additional-info h2 {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    text-align: center;
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .info-card {
    text-align: center;
    padding: 1.5rem;
  }
  
  .info-card h3 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.125rem;
  }
  
  .info-card p {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.6;
  }
  
  @media (max-width: 768px) {
    .contact-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }
    
    .info-grid {
      grid-template-columns: 1fr;
    }
  }
</style> 