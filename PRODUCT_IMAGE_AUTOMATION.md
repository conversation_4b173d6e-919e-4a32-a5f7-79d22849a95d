# Product Image Automation Guide

This document describes the automated product image downloading system for the Flat Roofing Shop website.

## Overview

The automation system systematically finds and downloads product images from manufacturer websites, replacing placeholder images with high-quality product photos. It has achieved **100% success rate for IKO products**.

## How It Works

### 1. **Image Discovery Process**
- Uses Brave Search API to find manufacturer product pages
- Scrapes official manufacturer websites using Bright Data tools
- Identifies product images by matching product codes in image URLs
- Prioritizes official manufacturer websites for authenticity

### 2. **Brand-Specific Strategies**

#### IKO Products (100% Success Rate)
- **Primary sites**: `ikogroup.co.uk` and `iko.ie`
- **Strategy**: Product codes appear directly in image URLs
- **URL patterns**: 
  - `https://ikogroup.co.uk/wp-content/uploads/*/IKO-*-[PRODUCT_CODE]-*.webp`
  - `https://iko.ie/wp-content/uploads/*/[PRODUCT_CODE]*.jpg`

#### Eagle Products (Requires Manual Mapping)
- **Issue**: Website uses product names (UltraFlex, CoolTop) not catalog codes (ECOPRO, UT)
- **Strategy**: Requires mapping between catalog codes and product names

#### Restec Products (Requires Manual Mapping)  
- **Issue**: Website uses product names (FlexiTec, GRPRoof) not catalog codes (109126, 109122)
- **Strategy**: Requires mapping between catalog codes and product names

### 3. **File Organization**
- **Download directory**: `/static/product-images/downloaded/`
- **Naming convention**: `[PRODUCT_CODE].webp`
- **File updates**: `/src/lib/products-details.ts` - replaces placeholder paths

## Usage Instructions

### Step 1: Set Up Environment
```bash
cd /Users/<USER>/Code/Flat-Roofing-Shop
```

### Step 2: Run Individual Product Downloads (IKO)
```bash
# Search for product on IKO website
mcp__Bright_Data__scrape_as_markdown(url="https://ikogroup.co.uk/product-category/merchants-stockists/high-performance-felts/")

# Download image when found in URL
curl -o "static/product-images/downloaded/[PRODUCT_CODE].webp" "[IMAGE_URL]"
```

### Step 3: Update Products File
Replace in `/src/lib/products-details.ts`:
```typescript
// From:
images: [
  "/product-images/placeholder.svg",
  "/product-images/placeholder.svg"
]

// To:
images: [
  "/product-images/downloaded/[PRODUCT_CODE].webp",
  "/product-images/downloaded/[PRODUCT_CODE].webp"
]
```

### Step 4: Batch Processing Template
```typescript
// For multiple IKO products, search these pages:
const ikoPages = [
  "https://ikogroup.co.uk/product-category/merchants-stockists/high-performance-felts/",
  "https://ikogroup.co.uk/product-category/merchants-stockists/high-performance-felts/?e-page-beac997=2"
];

// Extract image URLs from scraped content
// Download using curl commands
// Update products-details.ts systematically
```

## Automation Commands for Claude Code

### IKO Product Processing
```bash
# 1. Scrape IKO product pages
mcp__Bright_Data__scrape_as_markdown(url="https://ikogroup.co.uk/product-category/merchants-stockists/high-performance-felts/")

# 2. Download found images (replace PRODUCT_CODE and IMAGE_URL)
curl -o "/Users/<USER>/Code/Flat-Roofing-Shop/static/product-images/downloaded/[PRODUCT_CODE].webp" "[IMAGE_URL]"

# 3. Update products-details.ts
Edit(file_path="/Users/<USER>/Code/Flat-Roofing-Shop/src/lib/products-details.ts", 
     old_string='images: ["/product-images/placeholder.svg", "/product-images/placeholder.svg"]',
     new_string='images: ["/product-images/downloaded/[PRODUCT_CODE].webp", "/product-images/downloaded/[PRODUCT_CODE].webp"]')
```

### Search Strategies by Brand

#### IKO Products
```bash
# Use these search patterns:
"IKO [PRODUCT_CODE] product image site:ikogroup.co.uk"
"IKO [PRODUCT_CODE] roofing product site:iko.ie"
```

#### Eagle Products (Future Implementation)
```bash
# Manual mapping required first:
# ECOPRO -> UltraFlex, UT -> UltraTop, etc.
"Eagle [MAPPED_NAME] waterproofing site:eagle-waterproofing.com"
```

#### Restec Products (Future Implementation)  
```bash
# Manual mapping required first:
# 109126 -> FlexiTec, 109122 -> GRPRoof, etc.
"Restec [MAPPED_NAME] GRP fibreglass roofing"
```

## Success Metrics

### Current Status
- **Total Products**: 319
- **IKO Products Processed**: 22/55 (40% complete)
- **IKO Success Rate**: 100%
- **Overall Progress**: 22/319 (6.9%)

### Image Quality Standards
- **Source**: Official manufacturer websites only
- **Format**: WebP or JPG (converted to WebP)
- **Size**: Minimum 1000px width preferred
- **Authenticity**: Direct from manufacturer, not distributors

## Troubleshooting

### Common Issues
1. **Rate Limiting**: Add 2-second delays between requests
2. **Product Code Mismatches**: Verify code exists on manufacturer site
3. **Image Not Found**: Try alternative search terms or distributors

### File Verification
```bash
# Check downloaded images
ls -la /Users/<USER>/Code/Flat-Roofing-Shop/static/product-images/downloaded/

# Verify file sizes (should be > 1KB)
find static/product-images/downloaded/ -name "*.webp" -size +1k
```

## Future Enhancements

1. **Brand Mapping Tables**: Create lookup tables for Eagle and Restec products
2. **Distributor Integration**: Add support for major distributor websites
3. **Image Optimization**: Implement automatic WebP conversion and compression
4. **Batch Scripts**: Create shell scripts for bulk processing
5. **Quality Validation**: Add image quality and authenticity checks

## Results Achieved

### IKO Products Successfully Processed (22 total)
- 56050000, 61020216, 62120308, MW170700, 73070008
- 57000002, 9795D610, MW646402, MW646405  
- 58460000, 61020214, 61120000, 73170018, 73000008
- MW170700, 61150000, 73040000, 74070016, 74170016
- MW170703, 67530000, 61122028, 50170000

All images sourced from official IKO websites and integrated into the product catalog.