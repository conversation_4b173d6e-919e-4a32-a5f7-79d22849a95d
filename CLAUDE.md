# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start development server
- `npm run dev -- --open` - Start development server and open in browser
- `npm run build` - Create production build
- `npm run preview` - Preview production build
- `npm run check` - Type check the project
- `npm run check:watch` - Type check with watch mode

## Architecture

This is a SvelteKit-based e-commerce application for flat roofing products with the following key architectural patterns:

### Product Data Structure
Products are split across two data files that get combined at runtime:
- `src/lib/products-data.ts` - Core product information (code, brand, name, price, categories)
- `src/lib/products-details.ts` - Marketing content (slug, descriptions, images, meta)
- `src/lib/products.ts` - Combines both sources into unified Product interface

### State Management
- Cart state uses Svelte stores with localStorage persistence (`src/lib/stores/cart.ts`)
- Cart persists across browser sessions automatically
- Customer information is stored alongside cart items

### Layout Architecture
- Desktop: Fixed cart sidebar on right side
- Mobile: Sliding cart overlay triggered by header button
- Mobile cart state managed in `+layout.svelte` with event-driven toggle

### Brand System
- Brand information and descriptions in `src/lib/brandUtils.ts`
- Brand images in `static/brand-images/` (SVG and WebP formats)
- Dynamic brand pages with slug-based routing

### Routing Structure
- `/categories/[slug]` - Category listing pages
- `/products/[slug]` - Individual product pages  
- `/brands/[slug]` - Brand landing pages
- Both products and categories use server-side data loading

### Styling
- Uses LESS preprocessor
- CSS custom properties for primary colors defined in `+layout.svelte`
- Responsive design with mobile-first approach