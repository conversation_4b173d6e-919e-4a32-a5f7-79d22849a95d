#!/usr/bin/env python3
"""
Batch Product Image Downloader

This script processes all product codes and attempts to find and download
product images using the same approach that worked for IKO products.
"""

import json
import time
import re
import os
from pathlib import Path
from typing import List, Dict, Optional, Tuple

class BatchImageDownloader:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.download_dir = self.base_dir / "static/product-images/downloaded"
        self.product_codes_file = self.base_dir / "product-codes.txt"
        self.products_details_file = self.base_dir / "src/lib/products-details.ts"
        
        # Create download directory
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # Load product codes
        self.product_codes = self._load_product_codes()
        
        # Track progress
        self.processed = 0
        self.downloaded = 0
        self.failed = []
        self.success = []
        
        # Brand strategies
        self.brand_strategies = {
            'IKO': {
                'patterns': ['56050000', '61020216', '62120308', 'MW170700', '73070008', 
                           '4051600', '57000002', '9795D610', 'MW646402', 'MW646405', 'IKOFLASH'],
                'sites': ['site:ikogroup.co.uk', 'site:iko.ie'],
                'priority': 'high'
            },
            'EAGLE': {
                'patterns': ['ECOPRO', 'UT', 'ETC2C', 'UCT', 'UP', 'EA20', 'UM60M', 
                           'GR 100 VCL', 'VCP', 'UCC5', 'CT2.5', 'PCND'],
                'sites': ['site:eagle-waterproofing.com'],
                'priority': 'medium'
            },
            'RESTEC': {
                'patterns': ['109126', '109122', '109124', '109127', '109123', '109136', 
                           '160101', '160103', '104303', '104302', '104313', '104312', 
                           '104311', '102005', '109102'],
                'sites': ['restec', 'fibreglass', 'GRP'],
                'priority': 'medium'
            }
        }
    
    def _load_product_codes(self) -> List[str]:
        """Load product codes from file"""
        try:
            with open(self.product_codes_file, 'r') as f:
                codes = [line.strip() for line in f if line.strip()]
            print(f"Loaded {len(codes)} product codes")
            return codes
        except FileNotFoundError:
            print("Product codes file not found. Run extraction first.")
            return []
    
    def identify_brand(self, product_code: str) -> Tuple[str, Dict]:
        """Identify brand based on product code patterns"""
        for brand, strategy in self.brand_strategies.items():
            if any(pattern in product_code for pattern in strategy['patterns']):
                return brand, strategy
        return 'UNKNOWN', {'patterns': [], 'sites': [], 'priority': 'low'}
    
    def generate_search_queries(self, product_code: str) -> List[str]:
        """Generate search queries for a product code"""
        brand, strategy = self.identify_brand(product_code)
        
        queries = []
        
        if brand == 'IKO':
            queries.extend([
                f"IKO {product_code} product image site:ikogroup.co.uk",
                f"IKO {product_code} roofing product image site:iko.ie",
                f'"{product_code}" IKO roofing product image'
            ])
        elif brand == 'EAGLE':
            queries.extend([
                f"Eagle {product_code} waterproofing product image site:eagle-waterproofing.com",
                f"Eagle {product_code} resin product image",
                f'"{product_code}" Eagle roofing product'
            ])
        elif brand == 'RESTEC':
            queries.extend([
                f"Restec {product_code} fibreglass product image",
                f"Restec {product_code} GRP roofing product",
                f'"{product_code}" Restec product image'
            ])
        else:
            # Generic search for unknown brands
            queries.extend([
                f'"{product_code}" roofing product image',
                f'{product_code} building material product',
                f'{product_code} construction product image'
            ])
        
        return queries
    
    def create_search_commands(self, product_code: str) -> List[str]:
        """Create Claude Code commands for searching and downloading"""
        queries = self.generate_search_queries(product_code)
        
        commands = []
        
        # Search commands
        for i, query in enumerate(queries[:2]):  # Limit to 2 searches per product
            commands.append(f"""
# Search {i+1} for {product_code}
mcp__Brave__brave_web_search(query="{query}", count=5)
""")
        
        # If this is an IKO product, try direct URL patterns
        brand, _ = self.identify_brand(product_code)
        if brand == 'IKO':
            commands.append(f"""
# Try IKO direct URLs for {product_code}
# Check: https://ikogroup.co.uk/product-catalogue/
# Check: https://iko.ie/product/
""")
        
        return commands
    
    def create_download_command(self, product_code: str, image_url: str) -> str:
        """Create download command"""
        extension = '.webp' if image_url.endswith('.webp') else '.jpg'
        return f"""
# Download image for {product_code}
curl -o "{self.download_dir}/{product_code}{extension}" "{image_url}"
"""
    
    def create_update_command(self, product_code: str) -> str:
        """Create file update command"""
        return f"""
# Update products-details.ts for {product_code}
# Replace placeholder images with: /product-images/downloaded/{product_code}.webp
"""
    
    def generate_batch_script(self, batch_size: int = 50) -> str:
        """Generate a batch processing script"""
        
        script_content = f"""#!/bin/bash
# Batch Product Image Downloader
# Generated for {len(self.product_codes)} products

echo "Starting batch image download process..."
echo "Target: {len(self.product_codes)} products"
echo "Download directory: {self.download_dir}"
echo ""

# Progress tracking
TOTAL={len(self.product_codes)}
PROCESSED=0
DOWNLOADED=0
FAILED=0

# Create arrays for tracking
declare -a FAILED_CODES
declare -a SUCCESS_CODES

# Function to update progress
update_progress() {{
    PROCESSED=$((PROCESSED + 1))
    if [ $((PROCESSED % 10)) -eq 0 ]; then
        echo "Progress: $PROCESSED/$TOTAL (Downloaded: $DOWNLOADED, Failed: $FAILED)"
    fi
}}

# Function to log success
log_success() {{
    local code=$1
    DOWNLOADED=$((DOWNLOADED + 1))
    SUCCESS_CODES+=("$code")
    echo "✓ Successfully downloaded: $code"
}}

# Function to log failure
log_failure() {{
    local code=$1
    FAILED=$((FAILED + 1))
    FAILED_CODES+=("$code")
    echo "✗ Failed to download: $code"
}}

"""
        
        # Group products by brand priority
        iko_products = []
        eagle_products = []
        restec_products = []
        other_products = []
        
        for code in self.product_codes:
            brand, strategy = self.identify_brand(code)
            if brand == 'IKO':
                iko_products.append(code)
            elif brand == 'EAGLE':
                eagle_products.append(code)
            elif brand == 'RESTEC':
                restec_products.append(code)
            else:
                other_products.append(code)
        
        script_content += f"""
echo "Product breakdown:"
echo "  IKO products: {len(iko_products)}"
echo "  Eagle products: {len(eagle_products)}"  
echo "  Restec products: {len(restec_products)}"
echo "  Other products: {len(other_products)}"
echo ""

# Process IKO products first (highest success rate)
echo "Processing IKO products..."
"""
        
        # Add IKO products to script
        for code in iko_products:
            script_content += f"""
# Process {code} (IKO)
if [ ! -f "{self.download_dir}/{code}.webp" ] && [ ! -f "{self.download_dir}/{code}.jpg" ]; then
    echo "Searching for {code}..."
    # You would manually run the Claude Code search commands here
    # This script provides the framework for manual processing
    log_failure "{code}"  # Placeholder - update based on actual results
else
    echo "Image already exists for {code}"
    log_success "{code}"
fi
update_progress
sleep 2  # Rate limiting
"""
        
        script_content += """
# Generate final report
echo ""
echo "==============================================="
echo "BATCH PROCESSING COMPLETE"
echo "==============================================="
echo "Total processed: $PROCESSED"
echo "Successfully downloaded: $DOWNLOADED"
echo "Failed: $FAILED"
echo "Success rate: $(echo "scale=1; $DOWNLOADED * 100 / $TOTAL" | bc)%"
echo ""

if [ $FAILED -gt 0 ]; then
    echo "Failed product codes:"
    for code in "${FAILED_CODES[@]}"; do
        echo "  - $code"
    done
fi

echo ""
echo "Next steps:"
echo "1. Review failed codes for manual processing"
echo "2. Update products-details.ts with new image paths"
echo "3. Test image loading on the website"
"""
        
        return script_content
    
    def generate_claude_instructions(self) -> str:
        """Generate step-by-step instructions for processing with Claude Code"""
        
        instructions = f"""
# Automated Product Image Download Instructions
# Total products: {len(self.product_codes)}

## Setup Complete ✅
- Product codes extracted: {len(self.product_codes)} codes
- Download directory created: {self.download_dir}
- Brand strategies defined

## Phase 1: High-Priority Products (IKO - High Success Rate)

The following IKO products should be processed first as they have the highest success rate:
"""
        
        iko_products = [code for code in self.product_codes 
                       if self.identify_brand(code)[0] == 'IKO']
        
        for code in iko_products[:20]:  # First 20 IKO products
            instructions += f"""
### Product: {code}
1. Search: `IKO {code} product image site:ikogroup.co.uk OR site:iko.ie`
2. Look for official IKO product pages
3. Download image if found: `curl -o "{self.download_dir}/{code}.webp" "[IMAGE_URL]"`
4. Update products-details.ts: Replace placeholder with `/product-images/downloaded/{code}.webp`

"""
        
        instructions += f"""
## Phase 2: Medium-Priority Products (Eagle & Restec)

### Eagle Products ({len([c for c in self.product_codes if self.identify_brand(c)[0] == 'EAGLE'])} products):
- Search pattern: "Eagle [CODE] waterproofing product image site:eagle-waterproofing.com"
- Note: Product codes may not match current website

### Restec Products ({len([c for c in self.product_codes if self.identify_brand(c)[0] == 'RESTEC'])} products):
- Search pattern: "Restec [CODE] fibreglass GRP product image"
- Check distributor sites

## Phase 3: Generic Products

For unknown brands, use generic searches:
- "[CODE] roofing product image"
- "[CODE] building material product"

## Batch Processing Strategy

1. **Process in order of success probability**: IKO → Eagle → Restec → Generic
2. **Rate limiting**: 2-second delays between requests
3. **Progress tracking**: Update every 10 products
4. **Error handling**: Log failed codes for manual review

## Automation Commands

Use these commands in sequence for each product:

```bash
# For IKO products
mcp__Brave__brave_web_search(query="IKO [CODE] product image site:ikogroup.co.uk", count=5)
# Check results for official product pages
# If found: curl -o "static/product-images/downloaded/[CODE].webp" "[URL]"

# For Eagle products  
mcp__Brave__brave_web_search(query="Eagle [CODE] waterproofing site:eagle-waterproofing.com", count=5)

# For Restec products
mcp__Brave__brave_web_search(query="Restec [CODE] fibreglass GRP roofing", count=5)
```

## File Updates

After downloading each image, update products-details.ts:
```javascript
// Find and replace for each product
productCode: "[CODE]"
// Change:
images: ["/product-images/placeholder.svg", "/product-images/placeholder.svg"]
// To:
images: ["/product-images/downloaded/[CODE].webp", "/product-images/downloaded/[CODE].webp"]
```

## Expected Results

Based on the IKO success rate (100%), estimated success rates:
- IKO products: 90-100% success
- Eagle products: 30-60% success (code mismatches)
- Restec products: 40-70% success  
- Generic products: 20-40% success

Target: 60-70% overall success rate ({int(len(self.product_codes) * 0.65)} images)
"""
        
        return instructions
    
    def save_processing_files(self):
        """Save all processing files"""
        
        # Save batch script
        script_content = self.generate_batch_script()
        script_file = self.base_dir / "batch_download.sh"
        with open(script_file, 'w') as f:
            f.write(script_content)
        print(f"Batch script saved: {script_file}")
        
        # Save Claude instructions
        instructions = self.generate_claude_instructions()
        instructions_file = self.base_dir / "AUTOMATION_INSTRUCTIONS.md"
        with open(instructions_file, 'w') as f:
            f.write(instructions)
        print(f"Instructions saved: {instructions_file}")
        
        # Save product analysis
        analysis = self.analyze_products()
        analysis_file = self.base_dir / "product_analysis.json"
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2)
        print(f"Analysis saved: {analysis_file}")
    
    def analyze_products(self) -> Dict:
        """Analyze products by brand and type"""
        analysis = {
            'total_products': len(self.product_codes),
            'by_brand': {},
            'processing_order': [],
            'estimated_success_rates': {}
        }
        
        for code in self.product_codes:
            brand, strategy = self.identify_brand(code)
            
            if brand not in analysis['by_brand']:
                analysis['by_brand'][brand] = []
            analysis['by_brand'][brand].append(code)
        
        # Set processing order by priority
        brand_priority = ['IKO', 'EAGLE', 'RESTEC', 'UNKNOWN']
        for brand in brand_priority:
            if brand in analysis['by_brand']:
                analysis['processing_order'].extend(analysis['by_brand'][brand])
        
        # Estimated success rates based on manual testing
        analysis['estimated_success_rates'] = {
            'IKO': 0.95,
            'EAGLE': 0.45,
            'RESTEC': 0.55,
            'UNKNOWN': 0.30
        }
        
        return analysis

def main():
    print("🚀 Initializing Batch Product Image Downloader")
    print("=" * 50)
    
    downloader = BatchImageDownloader()
    
    print(f"📊 Analysis:")
    analysis = downloader.analyze_products()
    for brand, codes in analysis['by_brand'].items():
        print(f"  {brand}: {len(codes)} products")
    
    print(f"\n📁 Saving automation files...")
    downloader.save_processing_files()
    
    print(f"\n✅ Automation setup complete!")
    print(f"📋 Next steps:")
    print(f"   1. Review AUTOMATION_INSTRUCTIONS.md")
    print(f"   2. Process products using Claude Code MCP tools")
    print(f"   3. Monitor progress with batch_download.sh framework")
    
    print(f"\n🎯 Target: ~{int(len(downloader.product_codes) * 0.65)} successful downloads")

if __name__ == "__main__":
    main()