{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "Bash(rg:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm run check:*)", "Bash(find:*)", "Bash(tree:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "Bash(npm run preview:*)", "Bash(ls:*)", "mcp__Brave__brave_web_search", "mcp__Bright_Data__scrape_as_markdown", "mcp__ide__executeCode", "<PERSON><PERSON>(python3:*)"], "deny": []}}